package com.mdl.consult.bizdata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.SalesOrdersIncompleteMapper;
import com.mdl.consult.bizdata.domain.SalesOrdersIncomplete;
import com.mdl.consult.bizdata.service.ISalesOrdersIncompleteService;
import com.ruoyi.common.core.text.Convert;

/**
 * 未完成销售订单列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class SalesOrdersIncompleteServiceImpl implements ISalesOrdersIncompleteService
{
    @Autowired
    private SalesOrdersIncompleteMapper salesOrdersIncompleteMapper;
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;

    /**
     * 查询未完成销售订单列
     *
     * @param id 未完成销售订单列主键
     * @return 未完成销售订单列
     */
    @Override
    public SalesOrdersIncomplete selectSalesOrdersIncompleteById(Long id)
    {
        return salesOrdersIncompleteMapper.selectSalesOrdersIncompleteById(id);
    }

    /**
     * 查询未完成销售订单列列表
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 未完成销售订单列
     */
    @Override
    public List<SalesOrdersIncomplete> selectSalesOrdersIncompleteList(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        return salesOrdersIncompleteMapper.selectSalesOrdersIncompleteList(salesOrdersIncomplete);
    }

    /**
     * 新增未完成销售订单列
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 结果
     */
    @Override
    public int insertSalesOrdersIncomplete(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        salesOrdersIncomplete.setCreateTime(DateUtils.getNowDate());
        return salesOrdersIncompleteMapper.insertSalesOrdersIncomplete(salesOrdersIncomplete);
    }

    /**
     * 修改未完成销售订单列
     *
     * @param salesOrdersIncomplete 未完成销售订单列
     * @return 结果
     */
    @Override
    public int updateSalesOrdersIncomplete(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        salesOrdersIncomplete.setUpdateTime(DateUtils.getNowDate());
        return salesOrdersIncompleteMapper.updateSalesOrdersIncomplete(salesOrdersIncomplete);
    }

    /**
     * 批量删除未完成销售订单列
     *
     * @param ids 需要删除的未完成销售订单列主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersIncompleteByIds(String ids)
    {
        return salesOrdersIncompleteMapper.deleteSalesOrdersIncompleteByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除未完成销售订单列信息
     *
     * @param id 未完成销售订单列主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersIncompleteById(Long id)
    {
        return salesOrdersIncompleteMapper.deleteSalesOrdersIncompleteById(id);
    }

    /**
     * 根据数据分析ID删除未完成销售订单列数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteSalesOrdersIncompleteByDataAnalysisId(Long dataAnalysisId)
    {
        return salesOrdersIncompleteMapper.deleteSalesOrdersIncompleteByDataAnalysisId(dataAnalysisId);
    }

    public void calData(SalesOrdersIncomplete salesOrdersIncomplete) {
        BigDecimal quantity = Objects.requireNonNullElse(salesOrdersIncomplete.getQuantity(),new BigDecimal(0));
        /** 累计发货数量 */
        BigDecimal cumulativeShipment = Objects.requireNonNullElse(salesOrdersIncomplete.getCumulativeShipment(),new BigDecimal(0));
        /** 未发货数量 */
        BigDecimal unshippedQuantity = quantity.subtract(cumulativeShipment);

        BigDecimal inventory = StringUtils.isNotBlank(salesOrdersIncomplete.getInventoryCode()) ?
                materialShortageAnalysisMapper.getInventory(salesOrdersIncomplete.getDataAnalysisId(),salesOrdersIncomplete.getInventoryCode()) : new BigDecimal(0);

        salesOrdersIncomplete.setQuantity(quantity);
        salesOrdersIncomplete.setCumulativeShipment(cumulativeShipment);
        salesOrdersIncomplete.setUnshippedQuantity(unshippedQuantity);
        salesOrdersIncomplete.setCurrentBalance(Objects.requireNonNullElse(inventory,new BigDecimal(0)));
    }
}

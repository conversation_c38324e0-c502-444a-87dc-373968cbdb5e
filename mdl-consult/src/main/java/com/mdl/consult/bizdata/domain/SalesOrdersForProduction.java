package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 需要生产的销售订单对象 mdl_sales_orders_for_production
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class SalesOrdersForProduction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 订单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "订单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    /** 订单交期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "订单交期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 销售单号 */
    @Excel(name = "销售订单号")
    private String salesOrderNo;

    /** 生产单号 */
    @Excel(name = "生产订单号")
    private String productionOrderNo;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 存货编码 */
    @Excel(name = "存货编码")
    private String inventoryCode;

    /** 存货名称 */
    @Excel(name = "存货名称")
    private String inventoryName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 上月结存 */
    @Excel(name = "上月结存")
    private BigDecimal lastMonthBalance;

    /** 销售订单数量 */
    @Excel(name = "销售订单数量")
    private BigDecimal salesOrderQuantity;

    /** 本月应生产数 */
    @Excel(name = "本月应生产数")
    private BigDecimal monthlyProductionRequired;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setOrderDate(Date orderDate) 
    {
        this.orderDate = orderDate;
    }

    public Date getOrderDate() 
    {
        return orderDate;
    }

    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }

    public void setSalesOrderNo(String salesOrderNo) 
    {
        this.salesOrderNo = salesOrderNo;
    }

    public String getSalesOrderNo() 
    {
        return salesOrderNo;
    }

    public void setProductionOrderNo(String productionOrderNo) 
    {
        this.productionOrderNo = productionOrderNo;
    }

    public String getProductionOrderNo() 
    {
        return productionOrderNo;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setInventoryCode(String inventoryCode) 
    {
        this.inventoryCode = inventoryCode;
    }

    public String getInventoryCode() 
    {
        return inventoryCode;
    }

    public void setInventoryName(String inventoryName) 
    {
        this.inventoryName = inventoryName;
    }

    public String getInventoryName() 
    {
        return inventoryName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public void setLastMonthBalance(BigDecimal lastMonthBalance) 
    {
        this.lastMonthBalance = lastMonthBalance;
    }

    public BigDecimal getLastMonthBalance() 
    {
        return lastMonthBalance;
    }

    public void setSalesOrderQuantity(BigDecimal salesOrderQuantity) 
    {
        this.salesOrderQuantity = salesOrderQuantity;
    }

    public BigDecimal getSalesOrderQuantity() 
    {
        return salesOrderQuantity;
    }

    public void setMonthlyProductionRequired(BigDecimal monthlyProductionRequired) 
    {
        this.monthlyProductionRequired = monthlyProductionRequired;
    }

    public BigDecimal getMonthlyProductionRequired() 
    {
        return monthlyProductionRequired;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("orderDate", getOrderDate())
            .append("deliveryDate", getDeliveryDate())
            .append("salesOrderNo", getSalesOrderNo())
            .append("productionOrderNo", getProductionOrderNo())
            .append("customerName", getCustomerName())
            .append("inventoryCode", getInventoryCode())
            .append("inventoryName", getInventoryName())
            .append("specification", getSpecification())
            .append("unit", getUnit())
            .append("lastMonthBalance", getLastMonthBalance())
            .append("salesOrderQuantity", getSalesOrderQuantity())
            .append("monthlyProductionRequired", getMonthlyProductionRequired())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

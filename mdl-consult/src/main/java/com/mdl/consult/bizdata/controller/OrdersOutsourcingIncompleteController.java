package com.mdl.consult.bizdata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete;
import com.mdl.consult.bizdata.service.IOrdersOutsourcingIncompleteService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 未完成委外订单列Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/purchase-orders/outsourcing-incomplete")
public class OrdersOutsourcingIncompleteController extends BaseController
{
    private String prefix = "consult/bizdata/purchase-orders/outsourcing-incomplete";

    @Autowired
    private IOrdersOutsourcingIncompleteService ordersOutsourcingIncompleteService;

    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:view")
    @GetMapping()
    public String outsourcingIncomplete()
    {
        return prefix + "/outsourcing-incomplete";
    }

    /**
     * 查询未完成委外订单列列表
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        startPage();
        List<OrdersOutsourcingIncomplete> list = ordersOutsourcingIncompleteService.selectOrdersOutsourcingIncompleteList(ordersOutsourcingIncomplete);
        return getDataTable(list);
    }

    /**
     * 导出未完成委外订单列列表
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:export")
    @Log(title = "未完成委外订单列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        List<OrdersOutsourcingIncomplete> list = ordersOutsourcingIncompleteService.selectOrdersOutsourcingIncompleteList(ordersOutsourcingIncomplete);
        ExcelUtil<OrdersOutsourcingIncomplete> util = new ExcelUtil<OrdersOutsourcingIncomplete>(OrdersOutsourcingIncomplete.class);
        return util.exportExcel(list, "未完成委外订单列数据");
    }

    /**
     * 新增未完成委外订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存未完成委外订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:add")
    @Log(title = "未完成委外订单列", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        return toAjax(ordersOutsourcingIncompleteService.insertOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete));
    }

    /**
     * 修改未完成委外订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        OrdersOutsourcingIncomplete ordersOutsourcingIncomplete = ordersOutsourcingIncompleteService.selectOrdersOutsourcingIncompleteById(id);
        mmap.put("ordersOutsourcingIncomplete", ordersOutsourcingIncomplete);
        return prefix + "/edit";
    }

    /**
     * 修改保存未完成委外订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:edit")
    @Log(title = "未完成委外订单列", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(OrdersOutsourcingIncomplete ordersOutsourcingIncomplete)
    {
        return toAjax(ordersOutsourcingIncompleteService.updateOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete));
    }

    /**
     * 删除未完成委外订单列
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:remove")
    @Log(title = "未完成委外订单列", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(ordersOutsourcingIncompleteService.deleteOrdersOutsourcingIncompleteByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<OrdersOutsourcingIncomplete> util = new ExcelUtil<OrdersOutsourcingIncomplete>(OrdersOutsourcingIncomplete.class);
        return util.importTemplateExcel("未完成委外订单列数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:import")
    @Log(title = "未完成委外订单列", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<OrdersOutsourcingIncomplete> util = new ExcelUtil<OrdersOutsourcingIncomplete>(OrdersOutsourcingIncomplete.class);
        List<OrdersOutsourcingIncomplete> ordersOutsourcingIncompleteList = util.importExcel(file.getInputStream());
        String message = importOrdersOutsourcingIncomplete(dataAnalysisId,ordersOutsourcingIncompleteList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入未完成委外订单列数据
     *
     * @param dataAnalysisId
     * @param ordersOutsourcingIncompleteList 未完成委外订单列数据列表
     * @param isUpdateSupport                 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importOrdersOutsourcingIncomplete(Long dataAnalysisId, List<OrdersOutsourcingIncomplete> ordersOutsourcingIncompleteList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(ordersOutsourcingIncompleteList) || ordersOutsourcingIncompleteList.size() == 0)
        {
            throw new ServiceException("导入未完成委外订单列数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (OrdersOutsourcingIncomplete ordersOutsourcingIncomplete : ordersOutsourcingIncompleteList)
        {
            try
            {
                if(StringUtils.isBlank(ordersOutsourcingIncomplete.getMaterialLongCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    ordersOutsourcingIncomplete.setDataAnalysisId(dataAnalysisId);
                    ordersOutsourcingIncompleteService.insertOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + ordersOutsourcingIncomplete.getDataAnalysisId() + " 导入成功");
                }
                /* 验证是否存在这个未完成委外订单列
                OrdersOutsourcingIncomplete ooi = new OrdersOutsourcingIncomplete();
                ooi.setDataAnalysisId(ordersOutsourcingIncomplete.getDataAnalysisId());
                ooi.setOutsourcingOrderNo(ordersOutsourcingIncomplete.getOutsourcingOrderNo());
                List<OrdersOutsourcingIncomplete> list = ordersOutsourcingIncompleteService.selectOrdersOutsourcingIncompleteList(ooi);
                if (list.size() == 0)
                {
                    ordersOutsourcingIncompleteService.insertOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + ordersOutsourcingIncomplete.getDataAnalysisId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    ordersOutsourcingIncomplete.setId(list.get(0).getId());
                    ordersOutsourcingIncompleteService.updateOrdersOutsourcingIncomplete(ordersOutsourcingIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + ordersOutsourcingIncomplete.getDataAnalysisId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据分析ID " + ordersOutsourcingIncomplete.getDataAnalysisId() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据分析ID " + ordersOutsourcingIncomplete.getDataAnalysisId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据数据分析ID删除未完成委外订单列数据
     */
    @RequiresPermissions("consult/bizdata/purchase-orders:outsourcing-incomplete:remove")
    @Log(title = "未完成委外订单列", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(ordersOutsourcingIncompleteService.deleteOrdersOutsourcingIncompleteByDataAnalysisId(dataAnalysisId));
    }
}

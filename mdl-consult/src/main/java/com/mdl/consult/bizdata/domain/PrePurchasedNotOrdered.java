package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 已预购未下采购单对象 mdl_pre_purchased_not_ordered
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class PrePurchasedNotOrdered extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 预购日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "预购日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date prePurchaseDate;

    /** 预购编号 */
    @Excel(name = "预购编号")
    private String prePurchaseNo;

    /** 供应商 */
    @Excel(name = "供应商")
    private String supplier;

    /** 采购人员 */
    @Excel(name = "采购人员")
    private String purchaser;

    /** 物料编号 */
    @Excel(name = "物料编号")
    private String materialCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 型号 */
    @Excel(name = "型号")
    private String model;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 订单需求数量 */
    @Excel(name = "订单需求数量")
    private BigDecimal orderDemandQuantity;

    /** 已采购数量 */
    @Excel(name = "已采购数量")
    private BigDecimal purchasedQuantity;

    /** 剩余采购数量 */
    @Excel(name = "剩余采购数量")
    private BigDecimal remainingPurchaseQuantity;

    /** 采购状态 */
    @Excel(name = "采购状态")
    private String purchaseStatus;

    /** 交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 客供 */
    @Excel(name = "客供")
    private Integer customerSupplied;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 预购分类 */
    @Excel(name = "预购分类")
    private String prePurchaseCategory;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setPrePurchaseDate(Date prePurchaseDate) 
    {
        this.prePurchaseDate = prePurchaseDate;
    }

    public Date getPrePurchaseDate() 
    {
        return prePurchaseDate;
    }

    public void setPrePurchaseNo(String prePurchaseNo) 
    {
        this.prePurchaseNo = prePurchaseNo;
    }

    public String getPrePurchaseNo() 
    {
        return prePurchaseNo;
    }

    public void setSupplier(String supplier) 
    {
        this.supplier = supplier;
    }

    public String getSupplier() 
    {
        return supplier;
    }

    public void setPurchaser(String purchaser) 
    {
        this.purchaser = purchaser;
    }

    public String getPurchaser() 
    {
        return purchaser;
    }

    public void setMaterialCode(String materialCode) 
    {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() 
    {
        return materialCode;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    public void setModel(String model) 
    {
        this.model = model;
    }

    public String getModel() 
    {
        return model;
    }

    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public void setOrderDemandQuantity(BigDecimal orderDemandQuantity) 
    {
        this.orderDemandQuantity = orderDemandQuantity;
    }

    public BigDecimal getOrderDemandQuantity() 
    {
        return orderDemandQuantity;
    }

    public void setPurchasedQuantity(BigDecimal purchasedQuantity) 
    {
        this.purchasedQuantity = purchasedQuantity;
    }

    public BigDecimal getPurchasedQuantity() 
    {
        return purchasedQuantity;
    }

    public void setRemainingPurchaseQuantity(BigDecimal remainingPurchaseQuantity) 
    {
        this.remainingPurchaseQuantity = remainingPurchaseQuantity;
    }

    public BigDecimal getRemainingPurchaseQuantity() 
    {
        return remainingPurchaseQuantity;
    }

    public void setPurchaseStatus(String purchaseStatus) 
    {
        this.purchaseStatus = purchaseStatus;
    }

    public String getPurchaseStatus() 
    {
        return purchaseStatus;
    }

    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }

    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }

    public void setCustomerSupplied(Integer customerSupplied) 
    {
        this.customerSupplied = customerSupplied;
    }

    public Integer getCustomerSupplied() 
    {
        return customerSupplied;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setPrePurchaseCategory(String prePurchaseCategory) 
    {
        this.prePurchaseCategory = prePurchaseCategory;
    }

    public String getPrePurchaseCategory() 
    {
        return prePurchaseCategory;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("prePurchaseDate", getPrePurchaseDate())
            .append("prePurchaseNo", getPrePurchaseNo())
            .append("supplier", getSupplier())
            .append("purchaser", getPurchaser())
            .append("materialCode", getMaterialCode())
            .append("productName", getProductName())
            .append("model", getModel())
            .append("unit", getUnit())
            .append("orderDemandQuantity", getOrderDemandQuantity())
            .append("purchasedQuantity", getPurchasedQuantity())
            .append("remainingPurchaseQuantity", getRemainingPurchaseQuantity())
            .append("purchaseStatus", getPurchaseStatus())
            .append("deliveryDate", getDeliveryDate())
            .append("remarks", getRemarks())
            .append("customerSupplied", getCustomerSupplied())
            .append("customerName", getCustomerName())
            .append("prePurchaseCategory", getPrePurchaseCategory())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

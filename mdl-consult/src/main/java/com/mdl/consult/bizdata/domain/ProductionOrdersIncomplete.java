package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 未完成生产订单对象 mdl_production_orders_incomplete
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class ProductionOrdersIncomplete extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 单据日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "单据日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date documentDate;

    /** 生产单号 */
    @Excel(name = "生产订单号")
    private String productionOrderNo;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 销售单号 */
    @Excel(name = "销售订单号")
    private String salesOrderNo;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格 */
    @Excel(name = "规格")
    private String specification;

    /** 交易类型 */
    @Excel(name = "交易类型")
    private String transactionType;

    /** 排产时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "排产时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date schedulingTime;

    /** 开工时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "开工时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 订单状态 */
    @Excel(name = "订单状态")
    private String orderStatus;

    /** 生产数量 */
    @Excel(name = "生产数量")
    private BigDecimal productionQuantity;

    /** 累计入库数量 */
    @Excel(name = "累计入库数量")
    private BigDecimal cumulativeStorage;

    /** 未入库数量 */
    @Excel(name = "未入库数量")
    private BigDecimal unstoredQuantity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setDocumentDate(Date documentDate) 
    {
        this.documentDate = documentDate;
    }

    public Date getDocumentDate() 
    {
        return documentDate;
    }

    public void setProductionOrderNo(String productionOrderNo) 
    {
        this.productionOrderNo = productionOrderNo;
    }

    public String getProductionOrderNo() 
    {
        return productionOrderNo;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setSalesOrderNo(String salesOrderNo) 
    {
        this.salesOrderNo = salesOrderNo;
    }

    public String getSalesOrderNo() 
    {
        return salesOrderNo;
    }

    public void setMaterialCode(String materialCode) 
    {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() 
    {
        return materialCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setTransactionType(String transactionType) 
    {
        this.transactionType = transactionType;
    }

    public String getTransactionType() 
    {
        return transactionType;
    }

    public void setSchedulingTime(Date schedulingTime) 
    {
        this.schedulingTime = schedulingTime;
    }

    public Date getSchedulingTime() 
    {
        return schedulingTime;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setOrderStatus(String orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatus() 
    {
        return orderStatus;
    }

    public void setProductionQuantity(BigDecimal productionQuantity) 
    {
        this.productionQuantity = productionQuantity;
    }

    public BigDecimal getProductionQuantity() 
    {
        return productionQuantity;
    }

    public void setCumulativeStorage(BigDecimal cumulativeStorage) 
    {
        this.cumulativeStorage = cumulativeStorage;
    }

    public BigDecimal getCumulativeStorage() 
    {
        return cumulativeStorage;
    }

    public void setUnstoredQuantity(BigDecimal unstoredQuantity) 
    {
        this.unstoredQuantity = unstoredQuantity;
    }

    public BigDecimal getUnstoredQuantity() 
    {
        return unstoredQuantity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("documentDate", getDocumentDate())
            .append("productionOrderNo", getProductionOrderNo())
            .append("customerName", getCustomerName())
            .append("salesOrderNo", getSalesOrderNo())
            .append("materialCode", getMaterialCode())
            .append("materialName", getMaterialName())
            .append("specification", getSpecification())
            .append("transactionType", getTransactionType())
            .append("schedulingTime", getSchedulingTime())
            .append("startTime", getStartTime())
            .append("orderStatus", getOrderStatus())
            .append("productionQuantity", getProductionQuantity())
            .append("cumulativeStorage", getCumulativeStorage())
            .append("unstoredQuantity", getUnstoredQuantity())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

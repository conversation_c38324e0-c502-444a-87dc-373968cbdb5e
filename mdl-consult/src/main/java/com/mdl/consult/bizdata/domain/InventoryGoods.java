package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 库存商品数对象 mdl_inventory_goods
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class InventoryGoods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 仓库 */
    @Excel(name = "仓库")
    private String warehouse;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格说明 */
    @Excel(name = "规格说明")
    private String specification;

    /** 上月结存 */
    @Excel(name = "上月结存")
    private BigDecimal lastMonthBalance;

    /** 本月入库 */
    @Excel(name = "本月入库")
    private BigDecimal currentMonthInbound;

    /** 本月出库 */
    @Excel(name = "本月出库")
    private BigDecimal currentMonthOutbound;

    /** 本月结存数 */
    @Excel(name = "本月结存数")
    private BigDecimal currentMonthBalance;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setWarehouse(String warehouse) 
    {
        this.warehouse = warehouse;
    }

    public String getWarehouse() 
    {
        return warehouse;
    }

    public void setMaterialCode(String materialCode) 
    {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() 
    {
        return materialCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setLastMonthBalance(BigDecimal lastMonthBalance) 
    {
        this.lastMonthBalance = lastMonthBalance;
    }

    public BigDecimal getLastMonthBalance() 
    {
        return lastMonthBalance;
    }

    public void setCurrentMonthInbound(BigDecimal currentMonthInbound) 
    {
        this.currentMonthInbound = currentMonthInbound;
    }

    public BigDecimal getCurrentMonthInbound() 
    {
        return currentMonthInbound;
    }

    public void setCurrentMonthOutbound(BigDecimal currentMonthOutbound) 
    {
        this.currentMonthOutbound = currentMonthOutbound;
    }

    public BigDecimal getCurrentMonthOutbound() 
    {
        return currentMonthOutbound;
    }

    public void setCurrentMonthBalance(BigDecimal currentMonthBalance) 
    {
        this.currentMonthBalance = currentMonthBalance;
    }

    public BigDecimal getCurrentMonthBalance() 
    {
        return currentMonthBalance;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("warehouse", getWarehouse())
            .append("materialCode", getMaterialCode())
            .append("materialName", getMaterialName())
            .append("specification", getSpecification())
            .append("lastMonthBalance", getLastMonthBalance())
            .append("currentMonthInbound", getCurrentMonthInbound())
            .append("currentMonthOutbound", getCurrentMonthOutbound())
            .append("currentMonthBalance", getCurrentMonthBalance())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

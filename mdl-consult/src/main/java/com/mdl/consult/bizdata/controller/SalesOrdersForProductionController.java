package com.mdl.consult.bizdata.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.SalesOrdersForProduction;
import com.mdl.consult.bizdata.service.ISalesOrdersForProductionService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 需要生产的销售订单Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/sales-orders/production")
public class SalesOrdersForProductionController extends BaseController
{
    private String prefix = "consult/bizdata/sales-orders/production";

    @Autowired
    private ISalesOrdersForProductionService salesOrdersForProductionService;

    @RequiresPermissions("consult/bizdata/sales-orders:production:view")
    @GetMapping()
    public String production()
    {
        return prefix + "/production";
    }

    /**
     * 查询需要生产的销售订单列表
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SalesOrdersForProduction salesOrdersForProduction)
    {
        startPage();
        List<SalesOrdersForProduction> list = salesOrdersForProductionService.selectSalesOrdersForProductionList(salesOrdersForProduction);
        return getDataTable(list);
    }

    /**
     * 导出需要生产的销售订单列表
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:export")
    @Log(title = "需要生产的销售订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SalesOrdersForProduction salesOrdersForProduction)
    {
        List<SalesOrdersForProduction> list = salesOrdersForProductionService.selectSalesOrdersForProductionList(salesOrdersForProduction);
        ExcelUtil<SalesOrdersForProduction> util = new ExcelUtil<SalesOrdersForProduction>(SalesOrdersForProduction.class);
        return util.exportExcel(list, "需要生产的销售订单数据");
    }

    /**
     * 新增需要生产的销售订单
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存需要生产的销售订单
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:add")
    @Log(title = "需要生产的销售订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SalesOrdersForProduction salesOrdersForProduction)
    {
        return toAjax(salesOrdersForProductionService.insertSalesOrdersForProduction(salesOrdersForProduction));
    }

    /**
     * 修改需要生产的销售订单
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SalesOrdersForProduction salesOrdersForProduction = salesOrdersForProductionService.selectSalesOrdersForProductionById(id);
        mmap.put("salesOrdersForProduction", salesOrdersForProduction);
        return prefix + "/edit";
    }

    /**
     * 修改保存需要生产的销售订单
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:edit")
    @Log(title = "需要生产的销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SalesOrdersForProduction salesOrdersForProduction)
    {
        return toAjax(salesOrdersForProductionService.updateSalesOrdersForProduction(salesOrdersForProduction));
    }

    /**
     * 删除需要生产的销售订单
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:remove")
    @Log(title = "需要生产的销售订单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(salesOrdersForProductionService.deleteSalesOrdersForProductionByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/sales-orders:production:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<SalesOrdersForProduction> util = new ExcelUtil<SalesOrdersForProduction>(SalesOrdersForProduction.class);
        return util.importTemplateExcel("需要生产的销售订单数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/sales-orders:production:import")
    @Log(title = "需要生产的销售订单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<SalesOrdersForProduction> util = new ExcelUtil<SalesOrdersForProduction>(SalesOrdersForProduction.class);
        List<SalesOrdersForProduction> salesOrdersForProductionList = util.importExcel(file.getInputStream());
        String message = importSalesOrdersForProduction(dataAnalysisId,salesOrdersForProductionList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入需要生产的销售订单数据
     *
     * @param dataAnalysisId
     * @param salesOrdersForProductionList 需要生产的销售订单数据列表
     * @param isUpdateSupport              是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importSalesOrdersForProduction(Long dataAnalysisId, List<SalesOrdersForProduction> salesOrdersForProductionList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(salesOrdersForProductionList) || salesOrdersForProductionList.size() == 0)
        {
            throw new ServiceException("导入需要生产的销售订单数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SalesOrdersForProduction salesOrdersForProduction : salesOrdersForProductionList)
        {
            try
            {
                if(StringUtils.isBlank(salesOrdersForProduction.getInventoryCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、存货编码不能为空");
                }else {
                    salesOrdersForProduction.setDataAnalysisId(dataAnalysisId);
                    salesOrdersForProductionService.insertSalesOrdersForProduction(salesOrdersForProduction);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + salesOrdersForProduction.getSalesOrderNo() + " 导入成功");
                }

                /* 验证是否存在这个需要生产的销售订单
                SalesOrdersForProduction sofp = new SalesOrdersForProduction();
                sofp.setDataAnalysisId(salesOrdersForProduction.getDataAnalysisId());
                sofp.setSalesOrderNo(salesOrdersForProduction.getSalesOrderNo());
                List<SalesOrdersForProduction> list = salesOrdersForProductionService.selectSalesOrdersForProductionList(sofp);
                if (list.size() == 0)
                {
                    salesOrdersForProductionService.insertSalesOrdersForProduction(salesOrdersForProduction);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + salesOrdersForProduction.getSalesOrderNo() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    salesOrdersForProduction.setId(list.get(0).getId());
                    salesOrdersForProductionService.updateSalesOrdersForProduction(salesOrdersForProduction);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + salesOrdersForProduction.getSalesOrderNo() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据分析ID " + salesOrdersForProduction.getSalesOrderNo() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据分析ID " + salesOrdersForProduction.getDataAnalysisId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据数据分析ID删除需要生产的销售订单数据
     */
    @RequiresPermissions("consult/bizdata/sales-orders:production:remove")
    @Log(title = "需要生产的销售订单", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(salesOrdersForProductionService.deleteSalesOrdersForProductionByDataAnalysisId(dataAnalysisId));
    }
}

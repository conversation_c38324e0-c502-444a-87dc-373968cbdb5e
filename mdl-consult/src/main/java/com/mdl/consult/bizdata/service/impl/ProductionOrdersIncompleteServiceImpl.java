package com.mdl.consult.bizdata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.bizdata.mapper.ProductionOrdersIncompleteMapper;
import com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete;
import com.mdl.consult.bizdata.service.IProductionOrdersIncompleteService;
import com.ruoyi.common.core.text.Convert;

/**
 * 未完成生产订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class ProductionOrdersIncompleteServiceImpl implements IProductionOrdersIncompleteService
{
    @Autowired
    private ProductionOrdersIncompleteMapper productionOrdersIncompleteMapper;
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;

    /**
     * 查询未完成生产订单
     *
     * @param id 未完成生产订单主键
     * @return 未完成生产订单
     */
    @Override
    public ProductionOrdersIncomplete selectProductionOrdersIncompleteById(Long id)
    {
        return productionOrdersIncompleteMapper.selectProductionOrdersIncompleteById(id);
    }

    /**
     * 查询未完成生产订单列表
     *
     * @param productionOrdersIncomplete 未完成生产订单
     * @return 未完成生产订单
     */
    @Override
    public List<ProductionOrdersIncomplete> selectProductionOrdersIncompleteList(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        return productionOrdersIncompleteMapper.selectProductionOrdersIncompleteList(productionOrdersIncomplete);
    }

    /**
     * 新增未完成生产订单
     *
     * @param productionOrdersIncomplete 未完成生产订单
     * @return 结果
     */
    @Override
    public int insertProductionOrdersIncomplete(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        productionOrdersIncomplete.setCreateTime(DateUtils.getNowDate());
        return productionOrdersIncompleteMapper.insertProductionOrdersIncomplete(productionOrdersIncomplete);
    }

    /**
     * 修改未完成生产订单
     *
     * @param productionOrdersIncomplete 未完成生产订单
     * @return 结果
     */
    @Override
    public int updateProductionOrdersIncomplete(ProductionOrdersIncomplete productionOrdersIncomplete)
    {
        productionOrdersIncomplete.setUpdateTime(DateUtils.getNowDate());
        return productionOrdersIncompleteMapper.updateProductionOrdersIncomplete(productionOrdersIncomplete);
    }

    /**
     * 批量删除未完成生产订单
     *
     * @param ids 需要删除的未完成生产订单主键
     * @return 结果
     */
    @Override
    public int deleteProductionOrdersIncompleteByIds(String ids)
    {
        return productionOrdersIncompleteMapper.deleteProductionOrdersIncompleteByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除未完成生产订单信息
     *
     * @param id 未完成生产订单主键
     * @return 结果
     */
    @Override
    public int deleteProductionOrdersIncompleteById(Long id)
    {
        return productionOrdersIncompleteMapper.deleteProductionOrdersIncompleteById(id);
    }

    /**
     * 根据数据分析ID删除未完成生产订单数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteProductionOrdersIncompleteByDataAnalysisId(Long dataAnalysisId)
    {
        return productionOrdersIncompleteMapper.deleteProductionOrdersIncompleteByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(ProductionOrdersIncomplete productionOrdersIncomplete) {
        BigDecimal cumulativeStorage = Objects.requireNonNullElse(productionOrdersIncomplete.getCumulativeStorage(),new BigDecimal(0));
        BigDecimal productionQuantity = Objects.requireNonNullElse(
                materialShortageAnalysisMapper.getSaleOrderRequired(productionOrdersIncomplete.getDataAnalysisId(),productionOrdersIncomplete.getMaterialCode(),productionOrdersIncomplete.getSalesOrderNo()),
                new BigDecimal(0));

        BigDecimal unstoredQuantity = productionQuantity.subtract(cumulativeStorage);

        productionOrdersIncomplete.setCumulativeStorage(cumulativeStorage);
        productionOrdersIncomplete.setProductionQuantity(productionQuantity);
        productionOrdersIncomplete.setUnstoredQuantity(unstoredQuantity);
    }
}

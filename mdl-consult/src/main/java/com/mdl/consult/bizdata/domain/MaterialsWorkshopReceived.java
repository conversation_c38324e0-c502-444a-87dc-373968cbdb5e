package com.mdl.consult.bizdata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 车间已领对象 mdl_materials_workshop_received
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class MaterialsWorkshopReceived extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号（非数据库字段） */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 仓库 */
    @Excel(name = "仓库")
    private String warehouse;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格说明 */
    @Excel(name = "规格说明")
    private String specification;

    /** 现存量 */
    @Excel(name = "现存量")
    private BigDecimal currentQuantity;

    /** 可用量 */
    @Excel(name = "可用量")
    private BigDecimal availableQuantity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setWarehouse(String warehouse) 
    {
        this.warehouse = warehouse;
    }

    public String getWarehouse() 
    {
        return warehouse;
    }

    public void setMaterialCode(String materialCode) 
    {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() 
    {
        return materialCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setCurrentQuantity(BigDecimal currentQuantity) 
    {
        this.currentQuantity = currentQuantity;
    }

    public BigDecimal getCurrentQuantity() 
    {
        return currentQuantity;
    }

    public void setAvailableQuantity(BigDecimal availableQuantity) 
    {
        this.availableQuantity = availableQuantity;
    }

    public BigDecimal getAvailableQuantity() 
    {
        return availableQuantity;
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("warehouse", getWarehouse())
            .append("materialCode", getMaterialCode())
            .append("materialName", getMaterialName())
            .append("specification", getSpecification())
            .append("currentQuantity", getCurrentQuantity())
            .append("availableQuantity", getAvailableQuantity())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

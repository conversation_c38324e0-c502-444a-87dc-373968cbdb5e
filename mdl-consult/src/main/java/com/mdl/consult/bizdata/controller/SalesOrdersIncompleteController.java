package com.mdl.consult.bizdata.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import com.mdl.consult.bizdata.service.impl.SalesOrdersIncompleteServiceImpl;
import com.ruoyi.common.annotation.Excel;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.bizdata.domain.SalesOrdersIncomplete;
import com.mdl.consult.bizdata.service.ISalesOrdersIncompleteService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 未完成销售订单列Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/bizdata/sales-orders/incomplete")
public class SalesOrdersIncompleteController extends BaseController
{
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;
    private String prefix = "consult/bizdata/sales-orders/incomplete";

    @Autowired
    private ISalesOrdersIncompleteService salesOrdersIncompleteService;

    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:view")
    @GetMapping()
    public String incomplete()
    {
        return prefix + "/incomplete";
    }

    /**
     * 查询未完成销售订单列列表
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        startPage();
        List<SalesOrdersIncomplete> list = salesOrdersIncompleteService.selectSalesOrdersIncompleteList(salesOrdersIncomplete);
        return getDataTable(list);
    }

    /**
     * 导出未完成销售订单列列表
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:export")
    @Log(title = "未完成销售订单列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        List<SalesOrdersIncomplete> list = salesOrdersIncompleteService.selectSalesOrdersIncompleteList(salesOrdersIncomplete);
        ExcelUtil<SalesOrdersIncomplete> util = new ExcelUtil<SalesOrdersIncomplete>(SalesOrdersIncomplete.class);
        return util.exportExcel(list, "未完成销售订单列数据");
    }

    /**
     * 新增未完成销售订单列
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存未完成销售订单列
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:add")
    @Log(title = "未完成销售订单列", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        return toAjax(salesOrdersIncompleteService.insertSalesOrdersIncomplete(salesOrdersIncomplete));
    }

    /**
     * 修改未完成销售订单列
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SalesOrdersIncomplete salesOrdersIncomplete = salesOrdersIncompleteService.selectSalesOrdersIncompleteById(id);
        mmap.put("salesOrdersIncomplete", salesOrdersIncomplete);
        return prefix + "/edit";
    }

    /**
     * 修改保存未完成销售订单列
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:edit")
    @Log(title = "未完成销售订单列", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SalesOrdersIncomplete salesOrdersIncomplete)
    {
        return toAjax(salesOrdersIncompleteService.updateSalesOrdersIncomplete(salesOrdersIncomplete));
    }

    /**
     * 删除未完成销售订单列
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:remove")
    @Log(title = "未完成销售订单列", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(salesOrdersIncompleteService.deleteSalesOrdersIncompleteByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/bizdata/sales-orders:incomplete:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<SalesOrdersIncomplete> util = new ExcelUtil<SalesOrdersIncomplete>(SalesOrdersIncomplete.class);
        return util.importTemplateExcel("未完成销售订单列数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/bizdata/sales-orders:incomplete:import")
    @Log(title = "未完成销售订单列", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<SalesOrdersIncomplete> util = new ExcelUtil<SalesOrdersIncomplete>(SalesOrdersIncomplete.class);
        List<SalesOrdersIncomplete> salesOrdersIncompleteList = util.importExcel(file.getInputStream());
        String message = importSalesOrdersIncomplete(dataAnalysisId,salesOrdersIncompleteList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入未完成销售订单列数据
     *
     * @param dataAnalysisId
     * @param salesOrdersIncompleteList 未完成销售订单列数据列表
     * @param isUpdateSupport           是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importSalesOrdersIncomplete(Long dataAnalysisId, List<SalesOrdersIncomplete> salesOrdersIncompleteList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(salesOrdersIncompleteList) || salesOrdersIncompleteList.size() == 0)
        {
            throw new ServiceException("导入未完成销售订单列数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SalesOrdersIncomplete salesOrdersIncomplete : salesOrdersIncompleteList)
        {
            try
            {
                if(StringUtils.isBlank(salesOrdersIncomplete.getInventoryCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、存货编码不能为空");
                }else {
                    salesOrdersIncomplete.setDataAnalysisId(dataAnalysisId);
                    salesOrdersIncompleteService.insertSalesOrdersIncomplete(salesOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + salesOrdersIncomplete.getDataAnalysisId() + " 导入成功");
                }

                /* 验证是否存在这个未完成销售订单列
                SalesOrdersIncomplete soi = new SalesOrdersIncomplete();
                soi.setDataAnalysisId(salesOrdersIncomplete.getDataAnalysisId());
                soi.setSalesOrderNo(salesOrdersIncomplete.getSalesOrderNo());
                List<SalesOrdersIncomplete> list = salesOrdersIncompleteService.selectSalesOrdersIncompleteList(soi);
                if (list.size() == 0)
                {
                    salesOrdersIncompleteService.insertSalesOrdersIncomplete(salesOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + salesOrdersIncomplete.getDataAnalysisId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    salesOrdersIncomplete.setId(list.get(0).getId());
                    salesOrdersIncompleteService.updateSalesOrdersIncomplete(salesOrdersIncomplete);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据分析ID " + salesOrdersIncomplete.getDataAnalysisId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据分析ID " + salesOrdersIncomplete.getDataAnalysisId() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据分析ID " + salesOrdersIncomplete.getDataAnalysisId() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据数据分析ID删除未完成销售订单列数据
     */
    @RequiresPermissions("consult/bizdata/sales-orders:incomplete:remove")
    @Log(title = "未完成销售订单列", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(salesOrdersIncompleteService.deleteSalesOrdersIncompleteByDataAnalysisId(dataAnalysisId));
    }
}

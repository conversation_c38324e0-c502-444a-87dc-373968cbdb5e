package com.mdl.consult.analysis.controller;

import java.util.List;

import com.mdl.consult.basedata.domain.BomSemifinished;
import com.mdl.consult.basedata.domain.ProductSemifinished;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.analysis.domain.DataAnalysis;
import com.mdl.consult.analysis.service.IDataAnalysisService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.mdl.consult.basedata.service.IBomService;
import com.mdl.consult.basedata.service.IBomSemifinishedService;
import com.mdl.consult.basedata.service.IBomPackagingService;
import com.mdl.consult.basedata.service.ISafetyStockFinishedProductService;
import com.mdl.consult.basedata.service.ISafetyStockMaterialService;
import com.mdl.consult.basedata.service.IProductSemifinishedService;
import com.mdl.consult.bizdata.service.IInventoryGoodsService;
import com.mdl.consult.bizdata.service.ISalesOrdersIncompleteService;
import com.mdl.consult.bizdata.service.IOrdersOutsourcingIncompleteService;
import com.mdl.consult.bizdata.service.IPrePurchasedNotOrderedService;
import com.mdl.consult.bizdata.service.ISalesOrdersForProductionService;
import com.mdl.consult.bizdata.service.IMaterialsWorkshopReceivedService;
import com.mdl.consult.bizdata.service.IPurchaseOrdersIncompleteService;
import com.mdl.consult.bizdata.service.IProductionOrdersIncompleteService;

/**
 * 数据分析Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/analysis/analysis")
public class DataAnalysisController extends BaseController
{
    private String prefix = "consult/analysis/analysis";

    @Autowired
    private IDataAnalysisService dataAnalysisService;

    @Autowired
    private IBomService bomService;

    @Autowired
    private IBomSemifinishedService bomSemifinishedService;

    @Autowired
    private IBomPackagingService bomPackagingService;

    @Autowired
    private ISafetyStockFinishedProductService safetyStockFinishedProductService;

    @Autowired
    private ISafetyStockMaterialService safetyStockMaterialService;

    @Autowired
    private IProductSemifinishedService productSemifinishedService;

    @Autowired
    private IInventoryGoodsService inventoryGoodsService;

    @Autowired
    private ISalesOrdersIncompleteService salesOrdersIncompleteService;

    @Autowired
    private IOrdersOutsourcingIncompleteService ordersOutsourcingIncompleteService;

    @Autowired
    private IPrePurchasedNotOrderedService prePurchasedNotOrderedService;

    @Autowired
    private ISalesOrdersForProductionService salesOrdersForProductionService;

    @Autowired
    private IMaterialsWorkshopReceivedService materialsWorkshopReceivedService;

    @Autowired
    private IPurchaseOrdersIncompleteService purchaseOrdersIncompleteService;

    @Autowired
    private IProductionOrdersIncompleteService productionOrdersIncompleteService;

    @RequiresPermissions("consult/analysis:analysis:view")
    @GetMapping()
    public String analysis()
    {
        return prefix + "/analysis";
    }

    /**
     * 查询数据分析列表
     */
    @RequiresPermissions("consult/analysis:analysis:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DataAnalysis dataAnalysis)
    {
        startPage();
        List<DataAnalysis> list = dataAnalysisService.selectDataAnalysisList(dataAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出数据分析列表
     */
    @RequiresPermissions("consult/analysis:analysis:export")
    @Log(title = "数据分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(DataAnalysis dataAnalysis)
    {
        List<DataAnalysis> list = dataAnalysisService.selectDataAnalysisList(dataAnalysis);
        ExcelUtil<DataAnalysis> util = new ExcelUtil<DataAnalysis>(DataAnalysis.class);
        return util.exportExcel(list, "数据分析数据");
    }

    /**
     * 新增数据分析
     */
    @RequiresPermissions("consult/analysis:analysis:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存数据分析
     */
    @RequiresPermissions("consult/analysis:analysis:add")
    @Log(title = "数据分析", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(DataAnalysis dataAnalysis)
    {
        if (!dataAnalysisService.checkStatisticsNumberUnique(dataAnalysis))
        {
            return error("新增数据分析失败，统计编号'" + dataAnalysis.getStatisticsNumber() + "'已存在");
        }
        return toAjax(dataAnalysisService.insertDataAnalysis(dataAnalysis));
    }

    /**
     * 修改数据分析
     */
    @RequiresPermissions("consult/analysis:analysis:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        DataAnalysis dataAnalysis = dataAnalysisService.selectDataAnalysisById(id);
        mmap.put("dataAnalysis", dataAnalysis);
        return prefix + "/edit";
    }

    /**
     * 修改保存数据分析
     */
    @RequiresPermissions("consult/analysis:analysis:edit")
    @Log(title = "数据分析", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(DataAnalysis dataAnalysis)
    {
        if (!dataAnalysisService.checkStatisticsNumberUnique(dataAnalysis))
        {
            return error("修改数据分析失败，统计编号'" + dataAnalysis.getStatisticsNumber() + "'已存在");
        }
        return toAjax(dataAnalysisService.updateDataAnalysis(dataAnalysis));
    }

    /**
     * 删除数据分析
     */
    @RequiresPermissions("consult/analysis:analysis:remove")
    @Log(title = "数据分析", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(dataAnalysisService.deleteDataAnalysisByIds(ids));
    }

    /**
     * 检查BOM数据是否已导入
     */
    @PostMapping("/checkBomImported")
    @ResponseBody
    public AjaxResult checkBomImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.basedata.domain.Bom query = new com.mdl.consult.basedata.domain.Bom();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!bomService.selectBomList(query).isEmpty());
    }

    /**
     * 检查半成品BOM数据是否已导入
     */
    @PostMapping("/checkBomSemifinishedImported")
    @ResponseBody
    public AjaxResult checkBomSemifinishedImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        BomSemifinished query = new com.mdl.consult.basedata.domain.BomSemifinished();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!bomSemifinishedService.selectBomSemifinishedList(query).isEmpty());
    }

    /**
     * 检查包装BOM数据是否已导入
     */
    @PostMapping("/checkBomPackagingImported")
    @ResponseBody
    public AjaxResult checkBomPackagingImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.basedata.domain.BomPackaging query = new com.mdl.consult.basedata.domain.BomPackaging();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!bomPackagingService.selectBomPackagingList(query).isEmpty());
    }

    /**
     * 检查产成品安全库存数据是否已导入
     */
    @PostMapping("/checkSafetyStockFinishedProductImported")
    @ResponseBody
    public AjaxResult checkSafetyStockFinishedProductImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.basedata.domain.SafetyStockFinishedProduct query = new com.mdl.consult.basedata.domain.SafetyStockFinishedProduct();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!safetyStockFinishedProductService.selectSafetyStockFinishedProductList(query).isEmpty());
    }

    /**
     * 检查物料安全库存数据是否已导入
     */
    @PostMapping("/checkSafetyStockMaterialImported")
    @ResponseBody
    public AjaxResult checkSafetyStockMaterialImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.basedata.domain.SafetyStockMaterial query = new com.mdl.consult.basedata.domain.SafetyStockMaterial();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!safetyStockMaterialService.selectSafetyStockMaterialList(query).isEmpty());
    }

    /**
     * 检查产成品对应半成品数据是否已导入
     */
    @PostMapping("/checkProductSemifinishedImported")
    @ResponseBody
    public AjaxResult checkProductSemifinishedImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        ProductSemifinished query = new ProductSemifinished();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!productSemifinishedService.selectProductSemifinishedList(query).isEmpty());
    }

    /**
     * 检查库存商品数数据是否已导入
     */
    @PostMapping("/checkInventoryGoodsImported")
    @ResponseBody
    public AjaxResult checkInventoryGoodsImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.InventoryGoods query = new com.mdl.consult.bizdata.domain.InventoryGoods();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!inventoryGoodsService.selectInventoryGoodsList(query).isEmpty());
    }

    /**
     * 检查未完成销售订单数据是否已导入
     */
    @PostMapping("/checkSalesOrdersIncompleteImported")
    @ResponseBody
    public AjaxResult checkSalesOrdersIncompleteImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.SalesOrdersIncomplete query = new com.mdl.consult.bizdata.domain.SalesOrdersIncomplete();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!salesOrdersIncompleteService.selectSalesOrdersIncompleteList(query).isEmpty());
    }

    /**
     * 检查未完成委外订单数据是否已导入
     */
    @PostMapping("/checkOrdersOutsourcingIncompleteImported")
    @ResponseBody
    public AjaxResult checkOrdersOutsourcingIncompleteImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete query = new com.mdl.consult.bizdata.domain.OrdersOutsourcingIncomplete();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!ordersOutsourcingIncompleteService.selectOrdersOutsourcingIncompleteList(query).isEmpty());
    }

    /**
     * 检查已预约未下采购单数据是否已导入
     */
    @PostMapping("/checkPrePurchasedNotOrderedImported")
    @ResponseBody
    public AjaxResult checkPrePurchasedNotOrderedImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered query = new com.mdl.consult.bizdata.domain.PrePurchasedNotOrdered();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!prePurchasedNotOrderedService.selectPrePurchasedNotOrderedList(query).isEmpty());
    }

    /**
     * 检查需生产的销售单数据是否已导入
     */
    @PostMapping("/checkSalesOrdersForProductionImported")
    @ResponseBody
    public AjaxResult checkSalesOrdersForProductionImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.SalesOrdersForProduction query = new com.mdl.consult.bizdata.domain.SalesOrdersForProduction();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!salesOrdersForProductionService.selectSalesOrdersForProductionList(query).isEmpty());
    }

    /**
     * 检查车间已领数据是否已导入
     */
    @PostMapping("/checkMaterialsWorkshopReceivedImported")
    @ResponseBody
    public AjaxResult checkMaterialsWorkshopReceivedImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived query = new com.mdl.consult.bizdata.domain.MaterialsWorkshopReceived();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!materialsWorkshopReceivedService.selectMaterialsWorkshopReceivedList(query).isEmpty());
    }

    /**
     * 检查未完成采购单数据是否已导入
     */
    @PostMapping("/checkPurchaseOrdersIncompleteImported")
    @ResponseBody
    public AjaxResult checkPurchaseOrdersIncompleteImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete query = new com.mdl.consult.bizdata.domain.PurchaseOrdersIncomplete();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!purchaseOrdersIncompleteService.selectPurchaseOrdersIncompleteList(query).isEmpty());
    }

    /**
     * 检查未完成生产单数据是否已导入
     */
    @PostMapping("/checkProductionOrdersIncompleteImported")
    @ResponseBody
    public AjaxResult checkProductionOrdersIncompleteImported(String statisticsNumber)
    {
        // 根据统计编号查询数据是否存在
        Long dataAnalysisId = getDataAnalysisIdByStatisticsNumber(statisticsNumber);
        if (dataAnalysisId == null) {
            return AjaxResult.success(false);
        }

        // 创建查询对象
        com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete query = new com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete();
        query.setDataAnalysisId(dataAnalysisId);

        return AjaxResult.success(!productionOrdersIncompleteService.selectProductionOrdersIncompleteList(query).isEmpty());
    }

    /**
     * 欠料分析
     */
    @RequiresPermissions("consult/analysis:analysis:view")
    @GetMapping("/materialShortageAnalysis/{id}")
    public String materialShortageAnalysis(@PathVariable("id") Long id, ModelMap mmap)
    {
        DataAnalysis dataAnalysis = dataAnalysisService.selectDataAnalysisById(id);
        mmap.put("statisticsNumber", dataAnalysis.getStatisticsNumber());
        return "consult/analysis/material-shortage/material-shortage";
    }

    /**
     * 销售对比
     */
    @RequiresPermissions("consult/analysis:analysis:view")
    @GetMapping("/salesComparison/{id}")
    public String salesComparison(@PathVariable("id") Long id, ModelMap mmap)
    {
        DataAnalysis dataAnalysis = dataAnalysisService.selectDataAnalysisById(id);
        mmap.put("statisticsNumber", dataAnalysis.getStatisticsNumber());
        return "consult/analysis/sales-comparison/sales-comparison";
    }

    /**
     * 校验统计编号是否唯一
     */
    @PostMapping("/checkStatisticsNumberUnique")
    @ResponseBody
    public boolean checkStatisticsNumberUnique(DataAnalysis dataAnalysis)
    {
        return dataAnalysisService.checkStatisticsNumberUnique(dataAnalysis);
    }

    /**
     * 根据统计编号获取数据分析ID
     */
    private Long getDataAnalysisIdByStatisticsNumber(String statisticsNumber)
    {
        try {
            // 获取数据分析对象
            DataAnalysis dataAnalysis = new DataAnalysis();
            dataAnalysis.setStatisticsNumber(statisticsNumber);
            List<DataAnalysis> dataAnalysisList = dataAnalysisService.selectDataAnalysisList(dataAnalysis);
            if (dataAnalysisList.isEmpty()) {
                return null;
            }

            // 返回数据分析ID
            return dataAnalysisList.get(0).getId();
        } catch (Exception e) {
            return null;
        }
    }
}

package com.mdl.consult.analysis.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.mdl.consult.analysis.domain.MaterialShortageSummary;

/**
 * 欠料分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
public interface MaterialShortageAnalysisMapper
{
    /**
     * 计算欠料分析数据
     *
     * @return 欠料分析数据列表
     */
    List<MaterialShortageSummary> calculateMaterialShortage(MaterialShortageSummary queryParam);

    /**
     * 订单需求量
     * @param inventoryCode
     * @return
     */
    BigDecimal getSaleOrderRequired(Long dataAnalysisId,String inventoryCode,String salesOrderNo);

    /**
     * 库存数量
     * @param materialCode
     * @return
     */
    BigDecimal getInventory(Long dataAnalysisId,String materialCode);

    /**
     * 需生产量
     * @param parentCode
     * @return
     */
    BigDecimal getProductRequired(Long dataAnalysisId,String parentCode);
}

package com.mdl.consult.analysis.service.impl;

import java.util.List;

import com.mdl.consult.basedata.domain.BomPackaging;
import com.mdl.consult.basedata.domain.BomSemifinished;
import com.mdl.consult.basedata.domain.ProductSemifinished;
import com.mdl.consult.basedata.service.IBomPackagingService;
import com.mdl.consult.basedata.service.IBomSemifinishedService;
import com.mdl.consult.basedata.service.IProductSemifinishedService;
import com.mdl.consult.bizdata.domain.ProductionOrdersIncomplete;
import com.mdl.consult.bizdata.domain.SalesOrdersForProduction;
import com.mdl.consult.bizdata.domain.SalesOrdersIncomplete;
import com.mdl.consult.bizdata.service.IProductionOrdersIncompleteService;
import com.mdl.consult.bizdata.service.ISalesOrdersForProductionService;
import com.mdl.consult.bizdata.service.ISalesOrdersIncompleteService;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.analysis.mapper.DataAnalysisMapper;
import com.mdl.consult.analysis.domain.DataAnalysis;
import com.mdl.consult.analysis.service.IDataAnalysisService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.constant.UserConstants;

/**
 * 数据分析Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class DataAnalysisServiceImpl implements IDataAnalysisService
{
    @Autowired
    private DataAnalysisMapper dataAnalysisMapper;
    @Autowired
    private ISalesOrdersForProductionService salesOrdersForProductionService;
    @Autowired
    private IProductSemifinishedService productSemifinishedService;
    @Autowired
    private IBomSemifinishedService bomSemifinishedService;
    @Autowired
    private IBomPackagingService bomPackagingService;
    @Autowired
    private ISalesOrdersIncompleteService salesOrdersIncompleteService;
    @Autowired
    private IProductionOrdersIncompleteService productionOrdersIncompleteService;

    /**
     * 查询数据分析
     *
     * @param id 数据分析主键
     * @return 数据分析
     */
    @Override
    public DataAnalysis selectDataAnalysisById(Long id)
    {
        return dataAnalysisMapper.selectDataAnalysisById(id);
    }

    /**
     * 查询数据分析列表
     *
     * @param dataAnalysis 数据分析
     * @return 数据分析
     */
    @Override
    public List<DataAnalysis> selectDataAnalysisList(DataAnalysis dataAnalysis)
    {
        return dataAnalysisMapper.selectDataAnalysisList(dataAnalysis);
    }

    /**
     * 新增数据分析
     *
     * @param dataAnalysis 数据分析
     * @return 结果
     */
    @Override
    public int insertDataAnalysis(DataAnalysis dataAnalysis)
    {
        dataAnalysis.setCreateTime(DateUtils.getNowDate());
        return dataAnalysisMapper.insertDataAnalysis(dataAnalysis);
    }

    /**
     * 修改数据分析
     *
     * @param dataAnalysis 数据分析
     * @return 结果
     */
    @Override
    public int updateDataAnalysis(DataAnalysis dataAnalysis)
    {
        dataAnalysis.setUpdateTime(DateUtils.getNowDate());
        int updtNum = dataAnalysisMapper.updateDataAnalysis(dataAnalysis);
        calData(dataAnalysis.getId());
        return updtNum;
    }

    /**
     * 批量删除数据分析
     *
     * @param ids 需要删除的数据分析主键
     * @return 结果
     */
    @Override
    public int deleteDataAnalysisByIds(String ids)
    {
        return dataAnalysisMapper.deleteDataAnalysisByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除数据分析信息
     *
     * @param id 数据分析主键
     * @return 结果
     */
    @Override
    public int deleteDataAnalysisById(Long id)
    {
        return dataAnalysisMapper.deleteDataAnalysisById(id);
    }

    /**
     * 校验统计编号是否唯一
     *
     * @param dataAnalysis 数据分析信息
     * @return 结果
     */
    @Override
    public boolean checkStatisticsNumberUnique(DataAnalysis dataAnalysis)
    {
        Long id = StringUtils.isNull(dataAnalysis.getId()) ? -1L : dataAnalysis.getId();
        DataAnalysis info = dataAnalysisMapper.checkStatisticsNumberUnique(dataAnalysis.getStatisticsNumber());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void calData(Long dataAnalysisId) {
        //1、库存商品数
        //2、需生产的销售单
        SalesOrdersForProduction q = new SalesOrdersForProduction();
        q.setDataAnalysisId(dataAnalysisId);
        for (SalesOrdersForProduction salesOrdersForProduction : salesOrdersForProductionService.selectSalesOrdersForProductionList(q)) {
            salesOrdersForProductionService.calData(salesOrdersForProduction);
            salesOrdersForProductionService.updateSalesOrdersForProduction(salesOrdersForProduction);
        }

        //3、产成品对应半成品
        ProductSemifinished q1 = new ProductSemifinished();
        q1.setDataAnalysisId(dataAnalysisId);
        for (ProductSemifinished semifinished : productSemifinishedService.selectProductSemifinishedList(q1)) {
            productSemifinishedService.calData(semifinished);
            productSemifinishedService.updateProductSemifinished(semifinished);
        }
        //4、半成品BOM
        BomSemifinished q2 = new BomSemifinished();
        q2.setDataAnalysisId(dataAnalysisId);
        for (BomSemifinished bomSemifinished : bomSemifinishedService.selectBomSemifinishedList(q2)) {
            bomSemifinishedService.calData(bomSemifinished);
            bomSemifinishedService.updateBomSemifinished(bomSemifinished);
        }
        //5、包装BOM
        BomPackaging q3 = new BomPackaging();
        q3.setDataAnalysisId(dataAnalysisId);
        for (BomPackaging bomPackaging : bomPackagingService.selectBomPackagingList(q3)) {
            bomPackagingService.calData(bomPackaging);
            bomPackagingService.updateBomPackaging(bomPackaging);
        }
        //6、未完成销售订单
        SalesOrdersIncomplete q4 = new SalesOrdersIncomplete();
        q4.setDataAnalysisId(dataAnalysisId);
        for (SalesOrdersIncomplete salesOrdersIncomplete : salesOrdersIncompleteService.selectSalesOrdersIncompleteList(q4)) {
            salesOrdersIncompleteService.calData(salesOrdersIncomplete);
            salesOrdersIncompleteService.updateSalesOrdersIncomplete(salesOrdersIncomplete);
        }
        //7、未完成生产单
        ProductionOrdersIncomplete q5 = new ProductionOrdersIncomplete();
        q5.setDataAnalysisId(dataAnalysisId);
        for (ProductionOrdersIncomplete productionOrdersIncomplete : productionOrdersIncompleteService.selectProductionOrdersIncompleteList(q5)) {
            productionOrdersIncompleteService.calData(productionOrdersIncomplete);
            productionOrdersIncompleteService.updateProductionOrdersIncomplete(productionOrdersIncomplete);
        }
        //BOM
        //产成品安全库存
        //物料安全库存
        //未完成委外订单
        //已预约未下采购单
        //车间已领
        //未完成采购单
    }
}

package com.mdl.consult.analysis.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 欠料汇总对象 mdl_material_shortage_summary
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class MaterialShortageSummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 统计编号 */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 子件编码 */
    @Excel(name = "子件编码")
    private String childCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格说明 */
    @Excel(name = "规格说明")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 半成品物料需求小计 */
    @Excel(name = "半成品物料需求小计")
    private BigDecimal semifinishedMaterialRequirement;

    /** 包装物料需求小计 */
    @Excel(name = "包装物料需求小计")
    private BigDecimal packagingMaterialRequirement;

    /** 订单需求 */
    @Excel(name = "订单需求")
    private BigDecimal orderDemand;

    /** 库存物料 */
    @Excel(name = "库存物料")
    private BigDecimal inventoryMaterial;

    /** 采购未回 */
    @Excel(name = "采购未回")
    private BigDecimal purchaseUnreturned;

    /** 委外未回 */
    @Excel(name = "委外未回")
    private BigDecimal outsourcingUnreturned;

    /** 车间物料 */
    @Excel(name = "车间物料")
    private BigDecimal workshopMaterial;

    /** 已预购未采购 */
    @Excel(name = "已预购未采购")
    private BigDecimal prePurchasedNotPurchased;

    /** 安全库存 */
    @Excel(name = "安全库存")
    private BigDecimal safetyStock;

    /** 欠料 */
    @Excel(name = "欠料")
    private BigDecimal shortage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatisticsNumber(String statisticsNumber) 
    {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() 
    {
        return statisticsNumber;
    }

    public void setChildCode(String childCode) 
    {
        this.childCode = childCode;
    }

    public String getChildCode() 
    {
        return childCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public void setSemifinishedMaterialRequirement(BigDecimal semifinishedMaterialRequirement) 
    {
        this.semifinishedMaterialRequirement = semifinishedMaterialRequirement;
    }

    public BigDecimal getSemifinishedMaterialRequirement() 
    {
        return semifinishedMaterialRequirement;
    }

    public void setPackagingMaterialRequirement(BigDecimal packagingMaterialRequirement) 
    {
        this.packagingMaterialRequirement = packagingMaterialRequirement;
    }

    public BigDecimal getPackagingMaterialRequirement() 
    {
        return packagingMaterialRequirement;
    }

    public void setOrderDemand(BigDecimal orderDemand) 
    {
        this.orderDemand = orderDemand;
    }

    public BigDecimal getOrderDemand() 
    {
        return orderDemand;
    }

    public void setInventoryMaterial(BigDecimal inventoryMaterial) 
    {
        this.inventoryMaterial = inventoryMaterial;
    }

    public BigDecimal getInventoryMaterial() 
    {
        return inventoryMaterial;
    }

    public void setPurchaseUnreturned(BigDecimal purchaseUnreturned) 
    {
        this.purchaseUnreturned = purchaseUnreturned;
    }

    public BigDecimal getPurchaseUnreturned() 
    {
        return purchaseUnreturned;
    }

    public void setOutsourcingUnreturned(BigDecimal outsourcingUnreturned) 
    {
        this.outsourcingUnreturned = outsourcingUnreturned;
    }

    public BigDecimal getOutsourcingUnreturned() 
    {
        return outsourcingUnreturned;
    }

    public void setWorkshopMaterial(BigDecimal workshopMaterial) 
    {
        this.workshopMaterial = workshopMaterial;
    }

    public BigDecimal getWorkshopMaterial() 
    {
        return workshopMaterial;
    }

    public void setPrePurchasedNotPurchased(BigDecimal prePurchasedNotPurchased) 
    {
        this.prePurchasedNotPurchased = prePurchasedNotPurchased;
    }

    public BigDecimal getPrePurchasedNotPurchased() 
    {
        return prePurchasedNotPurchased;
    }

    public void setSafetyStock(BigDecimal safetyStock) 
    {
        this.safetyStock = safetyStock;
    }

    public BigDecimal getSafetyStock() 
    {
        return safetyStock;
    }

    public void setShortage(BigDecimal shortage) 
    {
        this.shortage = shortage;
    }

    public BigDecimal getShortage() 
    {
        return shortage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("childCode", getChildCode())
            .append("materialName", getMaterialName())
            .append("specification", getSpecification())
            .append("unit", getUnit())
            .append("semifinishedMaterialRequirement", getSemifinishedMaterialRequirement())
            .append("packagingMaterialRequirement", getPackagingMaterialRequirement())
            .append("orderDemand", getOrderDemand())
            .append("inventoryMaterial", getInventoryMaterial())
            .append("purchaseUnreturned", getPurchaseUnreturned())
            .append("outsourcingUnreturned", getOutsourcingUnreturned())
            .append("workshopMaterial", getWorkshopMaterial())
            .append("prePurchasedNotPurchased", getPrePurchasedNotPurchased())
            .append("safetyStock", getSafetyStock())
            .append("shortage", getShortage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

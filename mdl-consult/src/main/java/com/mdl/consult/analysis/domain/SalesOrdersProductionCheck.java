package com.mdl.consult.analysis.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 未完成的销售订单（与生产订单核对）对象 mdl_sales_orders_production_check
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class SalesOrdersProductionCheck extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    private String statisticsNumber;

    /** 订单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "订单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date orderDate;

    /** 单号 */
    @Excel(name = "单号")
    private String orderNo;

    /** 生产单号 */
    @Excel(name = "生产单号")
    private String productionOrderNo;

    /** 单号(重复字段) */
    @Excel(name = "单号(重复字段)")
    private String duplicateOrderNo;

    /** 客户编码 */
    @Excel(name = "客户编码")
    private String customerCode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 存货编码 */
    @Excel(name = "存货编码")
    private String inventoryCode;

    /** 存货名称 */
    @Excel(name = "存货名称")
    private String inventoryName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal quantity;

    /** 累计发货数量 */
    @Excel(name = "累计发货数量")
    private BigDecimal cumulativeShipment;

    /** 未发货数量 */
    @Excel(name = "未发货数量")
    private BigDecimal unshippedQuantity;

    /** 减现有：库存 */
    @Excel(name = "减现有：库存")
    private BigDecimal currentInventory;

    /** 需要生产数 */
    @Excel(name = "需要生产数")
    private BigDecimal productionNeeded;

    /** 实际在产数 */
    @Excel(name = "实际在产数")
    private BigDecimal actualInProduction;

    /** 差异数 */
    @Excel(name = "差异数")
    private BigDecimal difference;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataAnalysisId(Long dataAnalysisId) 
    {
        this.dataAnalysisId = dataAnalysisId;
    }

    public Long getDataAnalysisId() 
    {
        return dataAnalysisId;
    }

    public void setOrderDate(Date orderDate) 
    {
        this.orderDate = orderDate;
    }

    public Date getOrderDate() 
    {
        return orderDate;
    }

    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }

    public void setProductionOrderNo(String productionOrderNo) 
    {
        this.productionOrderNo = productionOrderNo;
    }

    public String getProductionOrderNo() 
    {
        return productionOrderNo;
    }

    public void setDuplicateOrderNo(String duplicateOrderNo) 
    {
        this.duplicateOrderNo = duplicateOrderNo;
    }

    public String getDuplicateOrderNo() 
    {
        return duplicateOrderNo;
    }

    public void setCustomerCode(String customerCode) 
    {
        this.customerCode = customerCode;
    }

    public String getCustomerCode() 
    {
        return customerCode;
    }

    public void setCustomerName(String customerName) 
    {
        this.customerName = customerName;
    }

    public String getCustomerName() 
    {
        return customerName;
    }

    public void setInventoryCode(String inventoryCode) 
    {
        this.inventoryCode = inventoryCode;
    }

    public String getInventoryCode() 
    {
        return inventoryCode;
    }

    public void setInventoryName(String inventoryName) 
    {
        this.inventoryName = inventoryName;
    }

    public String getInventoryName() 
    {
        return inventoryName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }

    public void setCumulativeShipment(BigDecimal cumulativeShipment) 
    {
        this.cumulativeShipment = cumulativeShipment;
    }

    public BigDecimal getCumulativeShipment() 
    {
        return cumulativeShipment;
    }

    public void setUnshippedQuantity(BigDecimal unshippedQuantity) 
    {
        this.unshippedQuantity = unshippedQuantity;
    }

    public BigDecimal getUnshippedQuantity() 
    {
        return unshippedQuantity;
    }

    public void setCurrentInventory(BigDecimal currentInventory) 
    {
        this.currentInventory = currentInventory;
    }

    public BigDecimal getCurrentInventory() 
    {
        return currentInventory;
    }

    public void setProductionNeeded(BigDecimal productionNeeded) 
    {
        this.productionNeeded = productionNeeded;
    }

    public BigDecimal getProductionNeeded() 
    {
        return productionNeeded;
    }

    public void setActualInProduction(BigDecimal actualInProduction) 
    {
        this.actualInProduction = actualInProduction;
    }

    public BigDecimal getActualInProduction() 
    {
        return actualInProduction;
    }

    public void setDifference(BigDecimal difference) 
    {
        this.difference = difference;
    }

    public BigDecimal getDifference() 
    {
        return difference;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataAnalysisId", getDataAnalysisId())
            .append("orderDate", getOrderDate())
            .append("orderNo", getOrderNo())
            .append("productionOrderNo", getProductionOrderNo())
            .append("duplicateOrderNo", getDuplicateOrderNo())
            .append("customerCode", getCustomerCode())
            .append("customerName", getCustomerName())
            .append("inventoryCode", getInventoryCode())
            .append("inventoryName", getInventoryName())
            .append("specification", getSpecification())
            .append("unit", getUnit())
            .append("quantity", getQuantity())
            .append("cumulativeShipment", getCumulativeShipment())
            .append("unshippedQuantity", getUnshippedQuantity())
            .append("currentInventory", getCurrentInventory())
            .append("productionNeeded", getProductionNeeded())
            .append("actualInProduction", getActualInProduction())
            .append("difference", getDifference())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public String getStatisticsNumber() {
        return statisticsNumber;
    }

    public void setStatisticsNumber(String statisticsNumber) {
        this.statisticsNumber = statisticsNumber;
    }
}

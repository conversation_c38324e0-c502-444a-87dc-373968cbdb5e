package com.mdl.consult.analysis.service;

import java.util.List;
import com.mdl.consult.analysis.domain.ImportTask;

/**
 * 导入任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IImportTaskService 
{
    /**
     * 查询导入任务
     * 
     * @param taskId 导入任务主键
     * @return 导入任务
     */
    public ImportTask selectImportTaskByTaskId(Long taskId);

    /**
     * 查询导入任务列表
     * 
     * @param importTask 导入任务
     * @return 导入任务集合
     */
    public List<ImportTask> selectImportTaskList(ImportTask importTask);

    /**
     * 新增导入任务
     * 
     * @param importTask 导入任务
     * @return 结果
     */
    public int insertImportTask(ImportTask importTask);

    /**
     * 修改导入任务
     * 
     * @param importTask 导入任务
     * @return 结果
     */
    public int updateImportTask(ImportTask importTask);

    /**
     * 批量删除导入任务
     * 
     * @param taskIds 需要删除的导入任务主键集合
     * @return 结果
     */
    public int deleteImportTaskByTaskIds(String taskIds);

    /**
     * 删除导入任务信息
     * 
     * @param taskId 导入任务主键
     * @return 结果
     */
    public int deleteImportTaskByTaskId(Long taskId);
    
    /**
     * 根据数据分析ID和导入类型查询最新的导入任务
     * 
     * @param dataAnalysisId 数据分析ID
     * @param importType 导入类型
     * @return 导入任务
     */
    public ImportTask selectLatestImportTask(Long dataAnalysisId, String importType);
    
    /**
     * 创建导入任务
     * 
     * @param dataAnalysisId 数据分析ID
     * @param statisticsNumber 统计编号
     * @param importType 导入类型
     * @return 任务ID
     */
    public Long createImportTask(Long dataAnalysisId, String statisticsNumber, String importType);
    
    /**
     * 更新任务状态为导入中
     * 
     * @param taskId 任务ID
     */
    public void updateTaskToImporting(Long taskId);
    
    /**
     * 更新任务状态为已完成
     * 
     * @param taskId 任务ID
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param resultMessage 结果信息
     */
    public void updateTaskToCompleted(Long taskId, Long successCount, Long failureCount, String resultMessage);
    
    /**
     * 更新任务状态为失败
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    public void updateTaskToFailed(Long taskId, String errorMessage);
}

package com.mdl.consult.basedata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * BOM对象 mdl_bom
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class Bom extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号 */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 母件编码 */
    @Excel(name = "母件编码")
    private String parentCode;

    /** BOM层级 */
    @Excel(name = "BOM层级")
    private Long bomLevel;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String materialName;

    /** 规格说明 */
    @Excel(name = "规格说明")
    private String specification;

    /** 子件数量 */
    @Excel(name = "子件数量")
    private BigDecimal childQuantity;

    /** 计量单位标准用量 */
    @Excel(name = "计量单位标准用量")
    private String measurementUnitStandard;

    /** 主单位标准用量 */
    @Excel(name = "主单位标准用量")
    private BigDecimal mainUnitStandard;

    /** 总母件数量 */
    @Excel(name = "总母件数量")
    private BigDecimal totalParentQuantity;

    /** 子件总数量 */
    @Excel(name = "子件总数量")
    private BigDecimal totalChildQuantity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatisticsNumber(String statisticsNumber) 
    {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() 
    {
        return statisticsNumber;
    }

    public void setParentCode(String parentCode) 
    {
        this.parentCode = parentCode;
    }

    public String getParentCode() 
    {
        return parentCode;
    }

    public void setBomLevel(Long bomLevel) 
    {
        this.bomLevel = bomLevel;
    }

    public Long getBomLevel() 
    {
        return bomLevel;
    }

    public void setMaterialCode(String materialCode) 
    {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() 
    {
        return materialCode;
    }

    public void setMaterialName(String materialName) 
    {
        this.materialName = materialName;
    }

    public String getMaterialName() 
    {
        return materialName;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setChildQuantity(BigDecimal childQuantity) 
    {
        this.childQuantity = childQuantity;
    }

    public BigDecimal getChildQuantity() 
    {
        return childQuantity;
    }

    public void setMeasurementUnitStandard(String measurementUnitStandard) 
    {
        this.measurementUnitStandard = measurementUnitStandard;
    }

    public String getMeasurementUnitStandard() 
    {
        return measurementUnitStandard;
    }

    public void setMainUnitStandard(BigDecimal mainUnitStandard) 
    {
        this.mainUnitStandard = mainUnitStandard;
    }

    public BigDecimal getMainUnitStandard() 
    {
        return mainUnitStandard;
    }

    public void setTotalParentQuantity(BigDecimal totalParentQuantity) 
    {
        this.totalParentQuantity = totalParentQuantity;
    }

    public BigDecimal getTotalParentQuantity() 
    {
        return totalParentQuantity;
    }

    public void setTotalChildQuantity(BigDecimal totalChildQuantity) 
    {
        this.totalChildQuantity = totalChildQuantity;
    }

    public BigDecimal getTotalChildQuantity() 
    {
        return totalChildQuantity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("parentCode", getParentCode())
            .append("bomLevel", getBomLevel())
            .append("materialCode", getMaterialCode())
            .append("materialName", getMaterialName())
            .append("specification", getSpecification())
            .append("childQuantity", getChildQuantity())
            .append("measurementUnitStandard", getMeasurementUnitStandard())
            .append("mainUnitStandard", getMainUnitStandard())
            .append("totalParentQuantity", getTotalParentQuantity())
            .append("totalChildQuantity", getTotalChildQuantity())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public Long getDataAnalysisId() {
        return dataAnalysisId;
    }

    public void setDataAnalysisId(Long dataAnalysisId) {
        this.dataAnalysisId = dataAnalysisId;
    }
}

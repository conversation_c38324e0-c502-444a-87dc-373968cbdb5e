package com.mdl.consult.basedata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.basedata.mapper.BomPackagingMapper;
import com.mdl.consult.basedata.domain.BomPackaging;
import com.mdl.consult.basedata.service.IBomPackagingService;
import com.ruoyi.common.core.text.Convert;

/**
 * 包装BOMService业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class BomPackagingServiceImpl implements IBomPackagingService
{
    @Autowired
    private BomPackagingMapper bomPackagingMapper;
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;

    /**
     * 查询包装BOM
     *
     * @param id 包装BOM主键
     * @return 包装BOM
     */
    @Override
    public BomPackaging selectBomPackagingById(Long id)
    {
        return bomPackagingMapper.selectBomPackagingById(id);
    }

    /**
     * 查询包装BOM列表
     *
     * @param bomPackaging 包装BOM
     * @return 包装BOM
     */
    @Override
    public List<BomPackaging> selectBomPackagingList(BomPackaging bomPackaging)
    {
        return bomPackagingMapper.selectBomPackagingList(bomPackaging);
    }

    /**
     * 新增包装BOM
     *
     * @param bomPackaging 包装BOM
     * @return 结果
     */
    @Override
    public int insertBomPackaging(BomPackaging bomPackaging)
    {
        bomPackaging.setCreateTime(DateUtils.getNowDate());
        return bomPackagingMapper.insertBomPackaging(bomPackaging);
    }

    /**
     * 修改包装BOM
     *
     * @param bomPackaging 包装BOM
     * @return 结果
     */
    @Override
    public int updateBomPackaging(BomPackaging bomPackaging)
    {
        bomPackaging.setUpdateTime(DateUtils.getNowDate());
        return bomPackagingMapper.updateBomPackaging(bomPackaging);
    }

    /**
     * 批量删除包装BOM
     *
     * @param ids 需要删除的包装BOM主键
     * @return 结果
     */
    @Override
    public int deleteBomPackagingByIds(String ids)
    {
        return bomPackagingMapper.deleteBomPackagingByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除包装BOM信息
     *
     * @param id 包装BOM主键
     * @return 结果
     */
    @Override
    public int deleteBomPackagingById(Long id)
    {
        return bomPackagingMapper.deleteBomPackagingById(id);
    }

    /**
     * 根据数据分析ID删除包装BOM数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteBomPackagingByDataAnalysisId(Long dataAnalysisId)
    {
        return bomPackagingMapper.deleteBomPackagingByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(BomPackaging bomPackaging) {
        BigDecimal orderQuantity = materialShortageAnalysisMapper.getSaleOrderRequired(bomPackaging.getDataAnalysisId(),bomPackaging.getParentCode(),null);
        bomPackaging.setOrderQuantity(Objects.requireNonNullElse(orderQuantity, new BigDecimal(0)));
        bomPackaging.setChildQuantity(Objects.requireNonNullElse(bomPackaging.getChildQuantity(), new BigDecimal(0)));

        bomPackaging.setMaterialRequirementSubtotal(bomPackaging.getChildQuantity().multiply(bomPackaging.getOrderQuantity()));
    }
}

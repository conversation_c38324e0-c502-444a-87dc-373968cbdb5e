package com.mdl.consult.basedata.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.basedata.domain.SafetyStockMaterial;
import com.mdl.consult.basedata.service.ISafetyStockMaterialService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 物料安全库存Controller
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/basedata/safety-stock/material")
public class SafetyStockMaterialController extends BaseController
{
    private String prefix = "consult/basedata/safety-stock/material";

    @Autowired
    private ISafetyStockMaterialService safetyStockMaterialService;

    @RequiresPermissions("consult/basedata/safety-stock:material:view")
    @GetMapping()
    public String material()
    {
        return prefix + "/material";
    }

    /**
     * 查询物料安全库存列表
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SafetyStockMaterial safetyStockMaterial)
    {
        startPage();
        List<SafetyStockMaterial> list = safetyStockMaterialService.selectSafetyStockMaterialList(safetyStockMaterial);
        return getDataTable(list);
    }

    /**
     * 导出物料安全库存列表
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:export")
    @Log(title = "物料安全库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SafetyStockMaterial safetyStockMaterial)
    {
        List<SafetyStockMaterial> list = safetyStockMaterialService.selectSafetyStockMaterialList(safetyStockMaterial);
        ExcelUtil<SafetyStockMaterial> util = new ExcelUtil<SafetyStockMaterial>(SafetyStockMaterial.class);
        return util.exportExcel(list, "物料安全库存数据");
    }

    /**
     * 新增物料安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存物料安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:add")
    @Log(title = "物料安全库存", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SafetyStockMaterial safetyStockMaterial)
    {
        return toAjax(safetyStockMaterialService.insertSafetyStockMaterial(safetyStockMaterial));
    }

    /**
     * 修改物料安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        SafetyStockMaterial safetyStockMaterial = safetyStockMaterialService.selectSafetyStockMaterialById(id);
        mmap.put("safetyStockMaterial", safetyStockMaterial);
        return prefix + "/edit";
    }

    /**
     * 修改保存物料安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:edit")
    @Log(title = "物料安全库存", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SafetyStockMaterial safetyStockMaterial)
    {
        return toAjax(safetyStockMaterialService.updateSafetyStockMaterial(safetyStockMaterial));
    }

    /**
     * 删除物料安全库存
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:remove")
    @Log(title = "物料安全库存", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(safetyStockMaterialService.deleteSafetyStockMaterialByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/basedata/safety-stock:material:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<SafetyStockMaterial> util = new ExcelUtil<SafetyStockMaterial>(SafetyStockMaterial.class);
        return util.importTemplateExcel("物料安全库存数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/basedata/safety-stock:material:import")
    @Log(title = "物料安全库存", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<SafetyStockMaterial> util = new ExcelUtil<SafetyStockMaterial>(SafetyStockMaterial.class);
        List<SafetyStockMaterial> safetyStockMaterialList = util.importExcel(file.getInputStream());
        String message = importSafetyStockMaterial(dataAnalysisId,safetyStockMaterialList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入物料安全库存数据
     *
     * @param dataAnalysisId
     * @param safetyStockMaterialList 物料安全库存数据列表
     * @param isUpdateSupport         是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importSafetyStockMaterial(Long dataAnalysisId, List<SafetyStockMaterial> safetyStockMaterialList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(safetyStockMaterialList) || safetyStockMaterialList.size() == 0)
        {
            throw new ServiceException("导入物料安全库存数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SafetyStockMaterial safetyStockMaterial : safetyStockMaterialList)
        {
            try
            {
                if(StringUtils.isBlank(safetyStockMaterial.getMaterialCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料编码不能为空");
                }else {
                    safetyStockMaterial.setDataAnalysisId(dataAnalysisId);
                    safetyStockMaterialService.insertSafetyStockMaterial(safetyStockMaterial);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、物料安全库存 " + safetyStockMaterial.getMaterialName() + " 导入成功");
                }

                /* 验证是否存在这个物料安全库存
                SafetyStockMaterial existMaterial = safetyStockMaterialService.selectSafetyStockMaterialById(safetyStockMaterial.getId());
                if (StringUtils.isNull(existMaterial))
                {
                    safetyStockMaterialService.insertSafetyStockMaterial(safetyStockMaterial);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、物料安全库存 " + safetyStockMaterial.getMaterialName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    safetyStockMaterialService.updateSafetyStockMaterial(safetyStockMaterial);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、物料安全库存 " + safetyStockMaterial.getMaterialName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、物料安全库存 " + safetyStockMaterial.getMaterialName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、物料安全库存 " + safetyStockMaterial.getMaterialName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据数据分析ID删除物料安全库存数据
     */
    @RequiresPermissions("consult/basedata/safety-stock:material:remove")
    @Log(title = "物料安全库存", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(safetyStockMaterialService.deleteSafetyStockMaterialByDataAnalysisId(dataAnalysisId));
    }
}

package com.mdl.consult.basedata.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.mdl.consult.basedata.domain.BomSemifinished;
import com.mdl.consult.basedata.service.IBomSemifinishedService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

/**
 * 半成品BOMController
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Controller
@RequestMapping("/consult/basedata/bom-semifinished")
public class BomSemifinishedController extends BaseController {
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;

    private String prefix = "consult/basedata/bom-semifinished";

    @Autowired
    private IBomSemifinishedService bomSemifinishedService;

    @RequiresPermissions("consult/basedata:bom-semifinished:view")
    @GetMapping()
    public String bomSemifinished()
    {
        return prefix + "/bom-semifinished";
    }

    /**
     * 查询半成品BOM列表
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BomSemifinished bomSemifinished)
    {
        startPage();
        List<BomSemifinished> list = bomSemifinishedService.selectBomSemifinishedList(bomSemifinished);
        return getDataTable(list);
    }

    /**
     * 导出半成品BOM列表
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:export")
    @Log(title = "半成品BOM", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BomSemifinished bomSemifinished)
    {
        List<BomSemifinished> list = bomSemifinishedService.selectBomSemifinishedList(bomSemifinished);
        ExcelUtil<BomSemifinished> util = new ExcelUtil<BomSemifinished>(BomSemifinished.class);
        return util.exportExcel(list, "半成品BOM数据");
    }

    /**
     * 新增半成品BOM
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存半成品BOM
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:add")
    @Log(title = "半成品BOM", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BomSemifinished bomSemifinished)
    {
        return toAjax(bomSemifinishedService.insertBomSemifinished(bomSemifinished));
    }

    /**
     * 修改半成品BOM
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        BomSemifinished bomSemifinished = bomSemifinishedService.selectBomSemifinishedById(id);
        mmap.put("bomSemifinished", bomSemifinished);
        return prefix + "/edit";
    }

    /**
     * 修改保存半成品BOM
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:edit")
    @Log(title = "半成品BOM", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BomSemifinished bomSemifinished)
    {
        return toAjax(bomSemifinishedService.updateBomSemifinished(bomSemifinished));
    }

    /**
     * 删除半成品BOM
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:remove")
    @Log(title = "半成品BOM", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(bomSemifinishedService.deleteBomSemifinishedByIds(ids));
    }

    /**
     * 下载模板
     */
    //@RequiresPermissions("consult/basedata:bom-semifinished:import")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate()
    {
        ExcelUtil<BomSemifinished> util = new ExcelUtil<BomSemifinished>(BomSemifinished.class);
        return util.importTemplateExcel("半成品BOM数据");
    }

    /**
     * 导入数据
     */
    //@RequiresPermissions("consult/basedata:bom-semifinished:import")
    @Log(title = "半成品BOM", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file,Long dataAnalysisId, boolean updateSupport) throws Exception
    {
        ExcelUtil<BomSemifinished> util = new ExcelUtil<BomSemifinished>(BomSemifinished.class);
        List<BomSemifinished> bomSemifinishedList = util.importExcel(file.getInputStream());
        String message = importBomSemifinished(dataAnalysisId,bomSemifinishedList, updateSupport);
        return AjaxResult.success(message);
    }

    /**
     * 导入半成品BOM数据
     *
     * @param dataAnalysisId
     * @param bomSemifinishedList 半成品BOM数据列表
     * @param isUpdateSupport     是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importBomSemifinished(Long dataAnalysisId, List<BomSemifinished> bomSemifinishedList, Boolean isUpdateSupport)
    {
        if (StringUtils.isNull(bomSemifinishedList) || bomSemifinishedList.size() == 0)
        {
            throw new ServiceException("导入半成品BOM数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BomSemifinished bomSemifinished : bomSemifinishedList)
        {
            try
            {
                if(StringUtils.isBlank(bomSemifinished.getChildCode())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、子件编码不能为空");
                }else {
                    bomSemifinished.setDataAnalysisId(dataAnalysisId);
                    bomSemifinishedService.insertBomSemifinished(bomSemifinished);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、半成品BOM " + bomSemifinished.getParentName() + " 导入成功");
                }
                /* 验证是否存在这个半成品BOM
                BomSemifinished b = bomSemifinishedService.selectBomSemifinishedById(bomSemifinished.getId());
                if (StringUtils.isNull(b))
                {
                    bomSemifinishedService.insertBomSemifinished(bomSemifinished);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、半成品BOM " + bomSemifinished.getParentName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    bomSemifinishedService.updateBomSemifinished(bomSemifinished);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、半成品BOM " + bomSemifinished.getParentName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、半成品BOM " + bomSemifinished.getParentName() + " 已存在");
                }*/
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、半成品BOM " + bomSemifinished.getParentName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据数据分析ID删除半成品BOM数据
     */
    @RequiresPermissions("consult/basedata:bom-semifinished:remove")
    @Log(title = "半成品BOM", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByDataAnalysisId")
    @ResponseBody
    public AjaxResult deleteByDataAnalysisId(Long dataAnalysisId)
    {
        return toAjax(bomSemifinishedService.deleteBomSemifinishedByDataAnalysisId(dataAnalysisId));
    }
}

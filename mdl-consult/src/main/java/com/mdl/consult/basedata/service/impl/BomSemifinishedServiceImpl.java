package com.mdl.consult.basedata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.basedata.mapper.BomSemifinishedMapper;
import com.mdl.consult.basedata.domain.BomSemifinished;
import com.mdl.consult.basedata.service.IBomSemifinishedService;
import com.ruoyi.common.core.text.Convert;

/**
 * 半成品BOMService业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class BomSemifinishedServiceImpl implements IBomSemifinishedService
{
    @Autowired
    private BomSemifinishedMapper bomSemifinishedMapper;
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;

    /**
     * 查询半成品BOM
     *
     * @param id 半成品BOM主键
     * @return 半成品BOM
     */
    @Override
    public BomSemifinished selectBomSemifinishedById(Long id)
    {
        return bomSemifinishedMapper.selectBomSemifinishedById(id);
    }

    /**
     * 查询半成品BOM列表
     *
     * @param bomSemifinished 半成品BOM
     * @return 半成品BOM
     */
    @Override
    public List<BomSemifinished> selectBomSemifinishedList(BomSemifinished bomSemifinished)
    {
        return bomSemifinishedMapper.selectBomSemifinishedList(bomSemifinished);
    }

    /**
     * 新增半成品BOM
     *
     * @param bomSemifinished 半成品BOM
     * @return 结果
     */
    @Override
    public int insertBomSemifinished(BomSemifinished bomSemifinished)
    {
        bomSemifinished.setCreateTime(DateUtils.getNowDate());
        return bomSemifinishedMapper.insertBomSemifinished(bomSemifinished);
    }

    /**
     * 修改半成品BOM
     *
     * @param bomSemifinished 半成品BOM
     * @return 结果
     */
    @Override
    public int updateBomSemifinished(BomSemifinished bomSemifinished)
    {
        bomSemifinished.setUpdateTime(DateUtils.getNowDate());
        return bomSemifinishedMapper.updateBomSemifinished(bomSemifinished);
    }

    /**
     * 批量删除半成品BOM
     *
     * @param ids 需要删除的半成品BOM主键
     * @return 结果
     */
    @Override
    public int deleteBomSemifinishedByIds(String ids)
    {
        return bomSemifinishedMapper.deleteBomSemifinishedByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除半成品BOM信息
     *
     * @param id 半成品BOM主键
     * @return 结果
     */
    @Override
    public int deleteBomSemifinishedById(Long id)
    {
        return bomSemifinishedMapper.deleteBomSemifinishedById(id);
    }

    /**
     * 根据数据分析ID删除半成品BOM数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteBomSemifinishedByDataAnalysisId(Long dataAnalysisId)
    {
        return bomSemifinishedMapper.deleteBomSemifinishedByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(BomSemifinished bomSemifinished) {
        if(bomSemifinished.getParentCode() != null) {
            BigDecimal orderQuantity = materialShortageAnalysisMapper.getProductRequired(bomSemifinished.getDataAnalysisId(),bomSemifinished.getParentCode());
            bomSemifinished.setOrderQuantity(Objects.requireNonNullElse(orderQuantity, new BigDecimal(0)));
            bomSemifinished.setChildQuantity(Objects.requireNonNullElse(bomSemifinished.getChildQuantity(), new BigDecimal(0)));

            bomSemifinished.setMaterialRequirementSubtotal(bomSemifinished.getChildQuantity().multiply(bomSemifinished.getOrderQuantity()));
        }
    }
}

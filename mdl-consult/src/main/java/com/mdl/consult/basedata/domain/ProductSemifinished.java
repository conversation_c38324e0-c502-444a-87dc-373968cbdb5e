package com.mdl.consult.basedata.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产成品对应的半成品对象 mdl_product_semifinished
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
public class ProductSemifinished extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据分析ID */
    @Excel(name = "数据分析ID")
    private Long dataAnalysisId;

    /** 统计编号 */
    @Excel(name = "统计编号")
    private String statisticsNumber;

    /** 中性编码 */
    @Excel(name = "半成品编码")
    private String neutralCode;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String materialCode;

    private Integer isMaster;

    /** 数量 */
    private BigDecimal quantity;

    /** 车间仓结存 */
    private BigDecimal workshopInventory;

    /** 应生产 */
    private BigDecimal productionRequired;

    @Excel(name = "编码1")
    private String code1;

    @Excel(name = "编码2")
    private String code2;

    @Excel(name = "编码3")
    private String code3;

    @Excel(name = "编码4")
    private String code4;

    @Excel(name = "编码5")
    private String code5;

    @Excel(name = "编码6")
    private String code6;

    @Excel(name = "编码7")
    private String code7;

    @Excel(name = "编码8")
    private String code8;

    @Excel(name = "编码9")
    private String code9;

    @Excel(name = "编码10")
    private String code10;

    @Excel(name = "编码11")
    private String code11;

    @Excel(name = "编码12")
    private String code12;

    @Excel(name = "编码13")
    private String code13;

    @Excel(name = "编码14")
    private String code14;

    @Excel(name = "编码15")
    private String code15;

    @Excel(name = "编码16")
    private String code16;

    @Excel(name = "编码17")
    private String code17;

    @Excel(name = "编码18")
    private String code18;

    @Excel(name = "编码19")
    private String code19;

    @Excel(name = "编码20")
    private String code20;

    @Excel(name = "编码21")
    private String code21;

    @Excel(name = "编码22")
    private String code22;

    @Excel(name = "编码23")
    private String code23;

    @Excel(name = "编码24")
    private String code24;

    @Excel(name = "编码25")
    private String code25;

    @Excel(name = "编码26")
    private String code26;

    @Excel(name = "编码27")
    private String code27;

    @Excel(name = "编码28")
    private String code28;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatisticsNumber(String statisticsNumber) 
    {
        this.statisticsNumber = statisticsNumber;
    }

    public String getStatisticsNumber() 
    {
        return statisticsNumber;
    }

    public void setNeutralCode(String neutralCode) 
    {
        this.neutralCode = neutralCode;
    }

    public String getNeutralCode() 
    {
        return neutralCode;
    }

    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }

    public void setWorkshopInventory(BigDecimal workshopInventory) 
    {
        this.workshopInventory = workshopInventory;
    }

    public BigDecimal getWorkshopInventory() 
    {
        return workshopInventory;
    }

    public void setProductionRequired(BigDecimal productionRequired) 
    {
        this.productionRequired = productionRequired;
    }

    public BigDecimal getProductionRequired() 
    {
        return productionRequired;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statisticsNumber", getStatisticsNumber())
            .append("neutralCode", getNeutralCode())
            .append("quantity", getQuantity())
            .append("workshopInventory", getWorkshopInventory())
            .append("productionRequired", getProductionRequired())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public Long getDataAnalysisId() {
        return dataAnalysisId;
    }

    public void setDataAnalysisId(Long dataAnalysisId) {
        this.dataAnalysisId = dataAnalysisId;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }


    public String getCode1() {
        return code1;
    }

    public void setCode1(String code1) {
        this.code1 = code1;
    }

    public String getCode2() {
        return code2;
    }

    public void setCode2(String code2) {
        this.code2 = code2;
    }

    public String getCode3() {
        return code3;
    }

    public void setCode3(String code3) {
        this.code3 = code3;
    }

    public String getCode4() {
        return code4;
    }

    public void setCode4(String code4) {
        this.code4 = code4;
    }

    public String getCode5() {
        return code5;
    }

    public void setCode5(String code5) {
        this.code5 = code5;
    }

    public String getCode6() {
        return code6;
    }

    public void setCode6(String code6) {
        this.code6 = code6;
    }

    public String getCode7() {
        return code7;
    }

    public void setCode7(String code7) {
        this.code7 = code7;
    }

    public String getCode8() {
        return code8;
    }

    public void setCode8(String code8) {
        this.code8 = code8;
    }

    public String getCode9() {
        return code9;
    }

    public void setCode9(String code9) {
        this.code9 = code9;
    }

    public String getCode10() {
        return code10;
    }

    public void setCode10(String code10) {
        this.code10 = code10;
    }

    public String getCode11() {
        return code11;
    }

    public void setCode11(String code11) {
        this.code11 = code11;
    }

    public String getCode12() {
        return code12;
    }

    public void setCode12(String code12) {
        this.code12 = code12;
    }

    public String getCode13() {
        return code13;
    }

    public void setCode13(String code13) {
        this.code13 = code13;
    }

    public String getCode14() {
        return code14;
    }

    public void setCode14(String code14) {
        this.code14 = code14;
    }

    public String getCode15() {
        return code15;
    }

    public void setCode15(String code15) {
        this.code15 = code15;
    }

    public String getCode16() {
        return code16;
    }

    public void setCode16(String code16) {
        this.code16 = code16;
    }

    public String getCode17() {
        return code17;
    }

    public void setCode17(String code17) {
        this.code17 = code17;
    }

    public String getCode18() {
        return code18;
    }

    public void setCode18(String code18) {
        this.code18 = code18;
    }

    public String getCode19() {
        return code19;
    }

    public void setCode19(String code19) {
        this.code19 = code19;
    }

    public String getCode20() {
        return code20;
    }

    public void setCode20(String code20) {
        this.code20 = code20;
    }

    public String getCode21() {
        return code21;
    }

    public void setCode21(String code21) {
        this.code21 = code21;
    }

    public String getCode22() {
        return code22;
    }

    public void setCode22(String code22) {
        this.code22 = code22;
    }

    public String getCode23() {
        return code23;
    }

    public void setCode23(String code23) {
        this.code23 = code23;
    }

    public String getCode24() {
        return code24;
    }

    public void setCode24(String code24) {
        this.code24 = code24;
    }

    public String getCode25() {
        return code25;
    }

    public void setCode25(String code25) {
        this.code25 = code25;
    }

    public String getCode26() {
        return code26;
    }

    public void setCode26(String code26) {
        this.code26 = code26;
    }

    public String getCode27() {
        return code27;
    }

    public void setCode27(String code27) {
        this.code27 = code27;
    }

    public String getCode28() {
        return code28;
    }

    public void setCode28(String code28) {
        this.code28 = code28;
    }

    public Integer getIsMaster() {
        return isMaster;
    }

    public void setIsMaster(Integer isMaster) {
        this.isMaster = isMaster;
    }
}

package com.mdl.consult.basedata.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.mdl.consult.basedata.mapper.ProductSemifinishedMapper;
import com.mdl.consult.basedata.domain.ProductSemifinished;
import com.mdl.consult.basedata.service.IProductSemifinishedService;
import com.ruoyi.common.core.text.Convert;

/**
 * 产成品对应的半成品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Service
public class ProductSemifinishedServiceImpl implements IProductSemifinishedService
{
    @Autowired
    private ProductSemifinishedMapper productSemifinishedMapper;
    @Autowired
    private MaterialShortageAnalysisMapper materialShortageAnalysisMapper;

    /**
     * 查询产成品对应的半成品
     *
     * @param id 产成品对应的半成品主键
     * @return 产成品对应的半成品
     */
    @Override
    public ProductSemifinished selectProductSemifinishedById(Long id)
    {
        return productSemifinishedMapper.selectProductSemifinishedById(id);
    }

    /**
     * 查询产成品对应的半成品列表
     *
     * @param productSemifinished 产成品对应的半成品
     * @return 产成品对应的半成品
     */
    @Override
    public List<ProductSemifinished> selectProductSemifinishedList(ProductSemifinished productSemifinished)
    {
        return productSemifinishedMapper.selectProductSemifinishedList(productSemifinished);
    }

    /**
     * 新增产成品对应的半成品
     *
     * @param productSemifinished 产成品对应的半成品
     * @return 结果
     */
    @Override
    public int insertProductSemifinished(ProductSemifinished productSemifinished)
    {
        productSemifinished.setCreateTime(DateUtils.getNowDate());
        return productSemifinishedMapper.insertProductSemifinished(productSemifinished);
    }

    /**
     * 修改产成品对应的半成品
     *
     * @param productSemifinished 产成品对应的半成品
     * @return 结果
     */
    @Override
    public int updateProductSemifinished(ProductSemifinished productSemifinished)
    {
        productSemifinished.setUpdateTime(DateUtils.getNowDate());
        return productSemifinishedMapper.updateProductSemifinished(productSemifinished);
    }

    /**
     * 批量删除产成品对应的半成品
     *
     * @param ids 需要删除的产成品对应的半成品主键
     * @return 结果
     */
    @Override
    public int deleteProductSemifinishedByIds(String ids)
    {
        return productSemifinishedMapper.deleteProductSemifinishedByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除产成品对应的半成品信息
     *
     * @param id 产成品对应的半成品主键
     * @return 结果
     */
    @Override
    public int deleteProductSemifinishedById(Long id)
    {
        return productSemifinishedMapper.deleteProductSemifinishedById(id);
    }

    /**
     * 根据数据分析ID删除产成品对应的半成品数据
     *
     * @param dataAnalysisId 数据分析ID
     * @return 结果
     */
    @Override
    public int deleteProductSemifinishedByDataAnalysisId(Long dataAnalysisId)
    {
        return productSemifinishedMapper.deleteProductSemifinishedByDataAnalysisId(dataAnalysisId);
    }

    @Override
    public void calData(ProductSemifinished semifinished) {
        if(Objects.equals(semifinished.getIsMaster(),1)){

            ProductSemifinished q = new ProductSemifinished();
            q.setDataAnalysisId(semifinished.getDataAnalysisId());
            q.setNeutralCode(semifinished.getNeutralCode());

            List<ProductSemifinished> productSemifinisheds = selectProductSemifinishedList(q);
            BigDecimal quantity = new BigDecimal(0);
            for (ProductSemifinished productSemifinished : productSemifinisheds) {
                if(StringUtils.isBlank(semifinished.getMaterialCode())) {
                    continue;
                }
                BigDecimal saleOrderRequired = materialShortageAnalysisMapper.getSaleOrderRequired(productSemifinished.getDataAnalysisId(),productSemifinished.getMaterialCode(),null);
                quantity = quantity.add(Objects.requireNonNullElse(saleOrderRequired,new BigDecimal(0)));
            }

            semifinished.setQuantity(quantity);
            BigDecimal workshopInventory = StringUtils.isNotBlank(semifinished.getMaterialCode()) ?
                    materialShortageAnalysisMapper.getInventory(semifinished.getDataAnalysisId(),semifinished.getNeutralCode()) : new BigDecimal(0);
            semifinished.setWorkshopInventory(Objects.requireNonNullElse(workshopInventory,new BigDecimal(0)));

            semifinished.setProductionRequired(quantity.subtract(Objects.requireNonNullElse(workshopInventory,new BigDecimal(0))));
        }
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mdl.consult.analysis.mapper.MaterialShortageAnalysisMapper">
    <!-- 计算欠料分析数据 -->
    <select id="calculateMaterialShortage" resultType="com.mdl.consult.analysis.domain.MaterialShortageSummary">
        select
            -- 子件编码
            b.child_code childCode,
            -- 物料名称
            b.child_name materialName,
            -- 物料规格
            b.specification specification,
            -- 库存物料
            t1.current_month_balance inventoryMaterial,
            -- 采购未回
            t2.purchaseUnreturned,
            -- 委外未回
            t3.outsourcingUnreturned,
            -- 车间物料
            t4.workshopMaterial,
            -- 已预购未采购
            t5.remainingPurchaseQuantity,
            -- 安全库存
            t6.safetyStock,
            -- 包装物料需求小计
            t7.packagingMaterialRequirement,
            -- 半成品物料需求小计
            t8.semifinishedMaterialRequirement,
            -- 订单需求
            (COALESCE(t7.packagingMaterialRequirement,0) + COALESCE(t8.semifinishedMaterialRequirement,0)) orderDemand,
            -- 欠料
            ((COALESCE(t7.packagingMaterialRequirement,0) + COALESCE(t8.semifinishedMaterialRequirement,0)) -
            COALESCE(t1.current_month_balance,0) -
            COALESCE(t2.purchaseUnreturned,0) -
            COALESCE(t3.outsourcingUnreturned,0) -
            COALESCE(t4.workshopMaterial,0) -
            COALESCE(t5.remainingPurchaseQuantity,0) -
            COALESCE(t6.safetyStock,0)) shortage
        from
        -- 需要分析的物料(合并半成品BOM和包装BOM)
        (select
             child_code,child_name,specification
         FROM (
            select child_code,child_name,specification from mdl_bom_semifinished where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            union all
            select child_code,child_name,specification from mdl_bom_packaging where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            ) t group by child_code,child_name,specification) b

        -- 库存物料
        left join (
            select material_code,sum(COALESCE(current_month_balance, 0)) current_month_balance
            from mdl_inventory_goods where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by material_code
        ) t1 on t1.material_code = b.child_code

        -- 采购未回
        left join (
            select material_code,sum(COALESCE(quantity, 0) - COALESCE(cumulative_storage, 0)) purchaseUnreturned
            from mdl_purchase_orders_incomplete where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by material_code
        ) t2 on t2.material_code = b.child_code

        -- 委外未回
        left join (
            select material_long_code,sum(COALESCE(sent_quantity, 0) - COALESCE(cumulative_storage, 0)) outsourcingUnreturned
            from mdl_orders_outsourcing_incomplete where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by material_long_code
        ) t3 on t3.material_long_code = b.child_code

        -- 车间物料
        left join (
            select material_code,sum(COALESCE(available_quantity, 0)) workshopMaterial
            from mdl_materials_workshop_received where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by material_code
        ) t4 on t4.material_code = b.child_code

        -- 已预购未采购
        left join (
            select material_code,sum(COALESCE(order_demand_quantity, 0) - COALESCE(purchased_quantity, 0)) remainingPurchaseQuantity
            from mdl_pre_purchased_not_ordered where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by material_code
        ) t5 on t5.material_code = b.child_code

        -- 安全库存
        left join (
            select material_code,sum(COALESCE(safety_stock, 0)) safetyStock
            from mdl_safety_stock_material where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by material_code
        ) t6 on t6.material_code = b.child_code

        -- 包装物料需求小计
        left join (
            select child_code,sum(COALESCE(material_requirement_subtotal, 0)) packagingMaterialRequirement
            from mdl_bom_packaging where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by child_code
        ) t7 on t7.child_code = b.child_code

        -- 半成品物料需求小计
        left join (
            select child_code,sum(COALESCE(material_requirement_subtotal, 0)) semifinishedMaterialRequirement
            from mdl_bom_semifinished where data_analysis_id = (select id from mdl_data_analysis where statistics_number = #{statisticsNumber})
            group by child_code
        ) t8 on t8.child_code = b.child_code
        <where>
            <if test="childCode != null and childCode != ''">
                and b.child_code = #{childCode}
            </if>
            <if test="materialName != null and materialName != ''">
                and b.child_name like concat('%', #{materialName}, '%')
            </if>
        </where>
    </select>

    <!-- 销售订单-需要生产数量 -->
    <select id="getSaleOrderRequired" resultType="java.math.BigDecimal">
        select sum(COALESCE(monthly_production_required ,0))
        from mdl_sales_orders_for_production
        <where>
            data_analysis_id = #{dataAnalysisId}
            <if test="inventoryCode != null and inventoryCode != ''">
                and inventory_code = #{inventoryCode}
            </if>
            <if test="salesOrderNo != null and salesOrderNo != ''">
                and sales_order_no = #{salesOrderNo}
            </if>
        </where>
    </select>

    <!-- 库存商品数-月结存 -->
    <select id="getInventory" resultType="java.math.BigDecimal">
        select sum(COALESCE(current_month_balance,0))
        from mdl_inventory_goods
        where material_code = #{materialCode}
        and data_analysis_id = #{dataAnalysisId}
    </select>

    <!-- 需生产量 (母件编码-中性编码)-->
    <select id="getProductRequired" resultType="java.math.BigDecimal">
        select sum(COALESCE(production_required ,0))
        from mdl_product_semifinished
        where neutral_code = #{parentCode}
        and data_analysis_id = #{dataAnalysisId}
    </select>

</mapper>

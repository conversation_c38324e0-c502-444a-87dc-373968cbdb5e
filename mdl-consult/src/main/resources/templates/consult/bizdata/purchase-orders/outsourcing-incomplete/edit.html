<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改未完成委外订单列')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-outsourcing-incomplete-edit" th:object="${ordersOutsourcingIncomplete}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数据分析ID：</label>
                    <div class="col-sm-8">
                        <input name="dataAnalysisId" th:field="*{dataAnalysisId}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">供应商：</label>
                    <div class="col-sm-8">
                        <input name="supplier" th:field="*{supplier}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">委外单号：</label>
                    <div class="col-sm-8">
                        <input name="outsourcingOrderNo" th:field="*{outsourcingOrderNo}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">订单日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="orderDate" th:value="${#dates.format(ordersOutsourcingIncomplete.orderDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">交货期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="deliveryDate" th:value="${#dates.format(ordersOutsourcingIncomplete.deliveryDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">物料长代码：</label>
                    <div class="col-sm-8">
                        <input name="materialLongCode" th:field="*{materialLongCode}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">物料名称：</label>
                    <div class="col-sm-8">
                        <input name="materialName" th:field="*{materialName}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">单位：</label>
                    <div class="col-sm-8">
                        <input name="unit" th:field="*{unit}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">规格型号：</label>
                    <div class="col-sm-8">
                        <input name="specification" th:field="*{specification}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">发出数量：</label>
                    <div class="col-sm-8">
                        <input name="sentQuantity" th:field="*{sentQuantity}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">累计入库数：</label>
                    <div class="col-sm-8">
                        <input name="cumulativeStorage" th:field="*{cumulativeStorage}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">未完成入库数：</label>
                    <div class="col-sm-8">
                        <input name="incompleteStorage" th:field="*{incompleteStorage}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/bizdata/purchase-orders/outsourcing-incomplete";
        $("#form-outsourcing-incomplete-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-outsourcing-incomplete-edit').serialize());
            }
        }

        $("input[name='orderDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='deliveryDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改数据分析')" />
    <style>
        .import-section {
            margin-top: 20px;
            border-top: 1px solid #e7eaec;
            padding-top: 15px;
        }
        .import-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .import-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .import-item-label {
            width: 200px;
            text-align: right;
            padding-right: 10px;
        }
        .import-btn {
            margin-right: 10px;
        }
        .btn-disabled {
            opacity: 0.65;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-analysis-edit" th:object="${dataAnalysis}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">统计编号：</label>
                    <div class="col-sm-8">
                        <input name="statisticsNumber" th:field="*{statisticsNumber}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="remark" class="form-control">[[*{remark}]]</textarea>
                    </div>
                </div>
            </div>

            <!-- 数据导入 -->
            <div class="col-xs-12 import-section">
                <div class="import-title">导入基础档案</div>
                <div class="import-item">
                    <div class="import-item-label">BOM：</div>
                    <div id="bom-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importBom()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-bom" onclick="deleteBom()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">半成品BOM：</div>
                    <div id="bom-semifinished-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importBomSemifinished()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-bom-semifinished" onclick="deleteBomSemifinished()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">包装BOM：</div>
                    <div id="bom-packaging-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importBomPackaging()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-bom-packaging" onclick="deleteBomPackaging()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">物料安全库存：</div>
                    <div id="safety-stock-material-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importSafetyStockMaterial()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-safety-stock-material" onclick="deleteSafetyStockMaterial()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">产成品安全库存：</div>
                    <div id="safety-stock-finished-product-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importSafetyStockFinishedProduct()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-safety-stock-finished-product" onclick="deleteSafetyStockFinishedProduct()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">产成品对应半成品：</div>
                    <div id="product-semifinished-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importProductSemifinished()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-product-semifinished" onclick="deleteProductSemifinished()" disabled>删除</button>
                </div>
            </div>
            <div class="col-xs-12 import-section">
                <div class="import-title">导入业务数据</div>
                <div class="import-item">
                    <div class="import-item-label">未完成销售订单：</div>
                    <div id="sales-orders-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importSalesOrdersIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-sales-orders-incomplete" onclick="deleteSalesOrdersIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">需生产的销售单：</div>
                    <div id="sales-orders-for-production-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importSalesOrdersForProduction()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-sales-orders-for-production" onclick="deleteSalesOrdersForProduction()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">未完成生产单：</div>
                    <div id="production-orders-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importProductionOrdersIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-production-orders-incomplete" onclick="deleteProductionOrdersIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">未完成采购单：</div>
                    <div id="purchase-orders-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importPurchaseOrdersIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-purchase-orders-incomplete" onclick="deletePurchaseOrdersIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">未完成委外订单：</div>
                    <div id="orders-outsourcing-incomplete-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importOrdersOutsourcingIncomplete()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-orders-outsourcing-incomplete" onclick="deleteOrdersOutsourcingIncomplete()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">已预约未下采购单：</div>
                    <div id="pre-purchased-not-ordered-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importPrePurchasedNotOrdered()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-pre-purchased-not-ordered" onclick="deletePrePurchasedNotOrdered()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">车间已领：</div>
                    <div id="materials-workshop-received-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importMaterialsWorkshopReceived()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-materials-workshop-received" onclick="deleteMaterialsWorkshopReceived()" disabled>删除</button>
                </div>
                <div class="import-item">
                    <div class="import-item-label">库存商品数：</div>
                    <div id="inventory-goods-status">未导入</div>
                    <button type="button" class="btn btn-primary import-btn" onclick="importInventoryGoods()">导入</button>
                    <button type="button" class="btn btn-danger" id="btn-delete-inventory-goods" onclick="deleteInventoryGoods()" disabled>删除</button>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "consult/analysis/analysis";
        var bomPrefix = ctx + "consult/basedata/bom";
        var bomSemifinishedPrefix = ctx + "consult/basedata/bom-semifinished";
        var bomPackagingPrefix = ctx + "consult/basedata/bom-package";
        var safetyStockFinishedProductPrefix = ctx + "consult/basedata/safety-stock/finished-product";
        var safetyStockMaterialPrefix = ctx + "consult/basedata/safety-stock/material";
        var productSemifinishedPrefix = ctx + "consult/basedata/product-semifinished";
        var inventoryGoodsPrefix = ctx + "consult/bizdata/goods";
        var salesOrdersIncompletePrefix = ctx + "consult/bizdata/sales-orders/incomplete";
        var ordersOutsourcingIncompletePrefix = ctx + "consult/bizdata/purchase-orders/outsourcing-incomplete";
        var prePurchasedNotOrderedPrefix = ctx + "consult/bizdata/purchase-orders/pre-purchased";
        var salesOrdersForProductionPrefix = ctx + "consult/bizdata/sales-orders/production";
        var materialsWorkshopReceivedPrefix = ctx + "consult/bizdata/material/received";
        var purchaseOrdersIncompletePrefix = ctx + "consult/bizdata/purchase-orders/incomplete";
        var productionOrdersIncompletePrefix = ctx + "consult/bizdata/production-orders/incomplete";

        $(function() {
            // 初始化页面时检查各项数据是否已导入
            checkAllImportStatus();
        });

        $("#form-analysis-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-analysis-edit').serialize());
            }
        }

        // 检查所有数据导入状态
        function checkAllImportStatus() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                return;
            }

            // 检查基础数据
            checkImportStatus("Bom", statisticsNumber);
            checkImportStatus("BomSemifinished", statisticsNumber);
            checkImportStatus("BomPackaging", statisticsNumber);
            checkImportStatus("SafetyStockFinishedProduct", statisticsNumber);
            checkImportStatus("SafetyStockMaterial", statisticsNumber);
            checkImportStatus("ProductSemifinished", statisticsNumber);

            // 检查业务数据
            checkImportStatus("InventoryGoods", statisticsNumber);
            checkImportStatus("SalesOrdersIncomplete", statisticsNumber);
            checkImportStatus("OrdersOutsourcingIncomplete", statisticsNumber);
            checkImportStatus("PrePurchasedNotOrdered", statisticsNumber);
            checkImportStatus("SalesOrdersForProduction", statisticsNumber);
            checkImportStatus("MaterialsWorkshopReceived", statisticsNumber);
            checkImportStatus("PurchaseOrdersIncomplete", statisticsNumber);
            checkImportStatus("ProductionOrdersIncomplete", statisticsNumber);
        }

        // 检查数据导入状态
        function checkImportStatus(dataType, statisticsNumber) {
            $.ajax({
                url: prefix + "/check" + dataType + "Imported",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.code == 0) {
                        var imported = result.data;
                        var statusId = getStatusId(dataType);
                        var btnId = getBtnId(dataType);

                        if (imported) {
                            $("#" + statusId).text("已导入");
                            $("#" + btnId).prop("disabled", false);
                        } else {
                            $("#" + statusId).text("未导入");
                            $("#" + btnId).prop("disabled", true);
                        }
                    }
                }
            });
        }

        // 获取状态显示元素ID
        function getStatusId(dataType) {
            var id = dataType.replace(/([A-Z])/g, "-$1").toLowerCase();
            if (id.startsWith("-")) {
                id = id.substring(1);
            }
            return id + "-status";
        }

        // 获取删除按钮ID
        function getBtnId(dataType) {
            var id = dataType.replace(/([A-Z])/g, "-$1").toLowerCase();
            if (id.startsWith("-")) {
                id = id.substring(1);
            }
            return "btn-delete-" + id;
        }

        // 获取导入URL
        function getImportUrl(dataType) {
            switch(dataType) {
                case "Bom": return bomPrefix + "/importData";
                case "BomSemifinished": return bomSemifinishedPrefix + "/importData";
                case "BomPackaging": return bomPackagingPrefix + "/importData";
                case "SafetyStockFinishedProduct": return safetyStockFinishedProductPrefix + "/importData";
                case "SafetyStockMaterial": return safetyStockMaterialPrefix + "/importData";
                case "ProductSemifinished": return productSemifinishedPrefix + "/importData";
                case "InventoryGoods": return inventoryGoodsPrefix + "/importData";
                case "SalesOrdersIncomplete": return salesOrdersIncompletePrefix + "/importData";
                case "OrdersOutsourcingIncomplete": return ordersOutsourcingIncompletePrefix + "/importData";
                case "PrePurchasedNotOrdered": return prePurchasedNotOrderedPrefix + "/importData";
                case "SalesOrdersForProduction": return salesOrdersForProductionPrefix + "/importData";
                case "MaterialsWorkshopReceived": return materialsWorkshopReceivedPrefix + "/importData";
                case "PurchaseOrdersIncomplete": return purchaseOrdersIncompletePrefix + "/importData";
                case "ProductionOrdersIncomplete": return productionOrdersIncompletePrefix + "/importData";
                default: return "";
            }
        }

        // 获取模板URL
        function getTemplateUrl(dataType) {
            switch(dataType) {
                case "Bom": return bomPrefix + "/importTemplate";
                case "BomSemifinished": return bomSemifinishedPrefix + "/importTemplate";
                case "BomPackaging": return bomPackagingPrefix + "/importTemplate";
                case "SafetyStockFinishedProduct": return safetyStockFinishedProductPrefix + "/importTemplate";
                case "SafetyStockMaterial": return safetyStockMaterialPrefix + "/importTemplate";
                case "ProductSemifinished": return productSemifinishedPrefix + "/importTemplate";
                case "InventoryGoods": return inventoryGoodsPrefix + "/importTemplate";
                case "SalesOrdersIncomplete": return salesOrdersIncompletePrefix + "/importTemplate";
                case "OrdersOutsourcingIncomplete": return ordersOutsourcingIncompletePrefix + "/importTemplate";
                case "PrePurchasedNotOrdered": return prePurchasedNotOrderedPrefix + "/importTemplate";
                case "SalesOrdersForProduction": return salesOrdersForProductionPrefix + "/importTemplate";
                case "MaterialsWorkshopReceived": return materialsWorkshopReceivedPrefix + "/importTemplate";
                case "PurchaseOrdersIncomplete": return purchaseOrdersIncompletePrefix + "/importTemplate";
                case "ProductionOrdersIncomplete": return productionOrdersIncompletePrefix + "/importTemplate";
                default: return "";
            }
        }

        // 导入数据通用方法
        function importData(dataType, title) {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }
            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;
                        // 打开导入对话框
                        $.modal.open(title, getImportUrl(dataType) + "?dataAnalysisId=" + dataAnalysisId, '400', '300');

                        // 导入完成后刷新状态
                        setTimeout(function() {
                            checkImportStatus(dataType, statisticsNumber);
                        }, 1000);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        // 各种导入方法
        function importBom() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("Bom", "导入BOM", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importBomSemifinished() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("BomSemifinished", "导入半成品BOM", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importBomPackaging() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("BomPackaging", "导入包装BOM", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSafetyStockFinishedProduct() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SafetyStockFinishedProduct", "导入产成品安全库存", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSafetyStockMaterial() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SafetyStockMaterial", "导入物料安全库存", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importProductSemifinished() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("ProductSemifinished", "导入产成品对应半成品", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importInventoryGoods() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("InventoryGoods", "导入库存商品数数据", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSalesOrdersIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SalesOrdersIncomplete", "导入未完成销售订单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importOrdersOutsourcingIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("OrdersOutsourcingIncomplete", "导入未完成委外订单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importPrePurchasedNotOrdered() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("PrePurchasedNotOrdered", "导入已预约未下采购单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importSalesOrdersForProduction() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("SalesOrdersForProduction", "导入需生产的销售单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importMaterialsWorkshopReceived() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("MaterialsWorkshopReceived", "导入车间已领", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importPurchaseOrdersIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("PurchaseOrdersIncomplete", "导入未完成采购单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        function importProductionOrdersIncomplete() {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            // 获取数据分析ID
            $.ajax({
                url: prefix + "/list",
                type: "post",
                dataType: "json",
                data: {
                    "statisticsNumber": statisticsNumber
                },
                success: function(result) {
                    if (result.rows && result.rows.length > 0) {
                        var dataAnalysisId = result.rows[0].id;

                        // 打开自定义导入对话框
                        openImportDialog("ProductionOrdersIncomplete", "导入未完成生产单", dataAnalysisId);
                    } else {
                        $.modal.alertWarning("未找到对应的数据分析记录，请先保存");
                    }
                }
            });
        }

        // 删除数据通用方法
        function deleteData(dataType, title) {
            var statisticsNumber = $("input[name='statisticsNumber']").val();
            if (!statisticsNumber) {
                $.modal.alertWarning("请先填写统计编号");
                return;
            }

            $.modal.confirm("确定要删除" + title + "数据吗？", function() {
                // 获取数据分析ID
                $.ajax({
                    url: prefix + "/list",
                    type: "post",
                    dataType: "json",
                    data: {
                        "statisticsNumber": statisticsNumber
                    },
                    success: function(result) {
                        if (result.rows && result.rows.length > 0) {
                            var dataAnalysisId = result.rows[0].id;

                            // 根据数据类型获取删除URL
                            var deleteUrl = getDeleteUrl(dataType);

                            // 执行删除操作
                            $.ajax({
                                url: deleteUrl,
                                type: "post",
                                dataType: "json",
                                data: {
                                    "dataAnalysisId": dataAnalysisId
                                },
                                success: function(result) {
                                    if (result.code == 0) {
                                        $.modal.msgSuccess("删除成功");
                                        // 刷新状态
                                        checkImportStatus(dataType, statisticsNumber);
                                    } else {
                                        $.modal.alertError(result.msg);
                                    }
                                }
                            });
                        } else {
                            $.modal.alertWarning("未找到对应的数据分析记录");
                        }
                    }
                });
            });
        }

        // 获取删除URL
        function getDeleteUrl(dataType) {
            switch(dataType) {
                case "Bom": return bomPrefix + "/deleteByDataAnalysisId";
                case "BomSemifinished": return bomSemifinishedPrefix + "/deleteByDataAnalysisId";
                case "BomPackaging": return bomPackagingPrefix + "/deleteByDataAnalysisId";
                case "SafetyStockFinishedProduct": return safetyStockFinishedProductPrefix + "/deleteByDataAnalysisId";
                case "SafetyStockMaterial": return safetyStockMaterialPrefix + "/deleteByDataAnalysisId";
                case "ProductSemifinished": return productSemifinishedPrefix + "/deleteByDataAnalysisId";
                case "InventoryGoods": return inventoryGoodsPrefix + "/deleteByDataAnalysisId";
                case "SalesOrdersIncomplete": return salesOrdersIncompletePrefix + "/deleteByDataAnalysisId";
                case "OrdersOutsourcingIncomplete": return ordersOutsourcingIncompletePrefix + "/deleteByDataAnalysisId";
                case "PrePurchasedNotOrdered": return prePurchasedNotOrderedPrefix + "/deleteByDataAnalysisId";
                case "SalesOrdersForProduction": return salesOrdersForProductionPrefix + "/deleteByDataAnalysisId";
                case "MaterialsWorkshopReceived": return materialsWorkshopReceivedPrefix + "/deleteByDataAnalysisId";
                case "PurchaseOrdersIncomplete": return purchaseOrdersIncompletePrefix + "/deleteByDataAnalysisId";
                case "ProductionOrdersIncomplete": return productionOrdersIncompletePrefix + "/deleteByDataAnalysisId";
                default: return "";
            }
        }

        // 各种删除方法
        function deleteBom() {
            deleteData("Bom", "BOM");
        }

        function deleteBomSemifinished() {
            deleteData("BomSemifinished", "半成品BOM");
        }

        function deleteBomPackaging() {
            deleteData("BomPackaging", "包装BOM");
        }

        function deleteSafetyStockFinishedProduct() {
            deleteData("SafetyStockFinishedProduct", "产成品安全库存");
        }

        function deleteSafetyStockMaterial() {
            deleteData("SafetyStockMaterial", "物料安全库存");
        }

        function deleteProductSemifinished() {
            deleteData("ProductSemifinished", "产成品对应半成品");
        }

        function deleteInventoryGoods() {
            deleteData("InventoryGoods", "库存商品数");
        }

        function deleteSalesOrdersIncomplete() {
            deleteData("SalesOrdersIncomplete", "未完成销售订单");
        }

        function deleteOrdersOutsourcingIncomplete() {
            deleteData("OrdersOutsourcingIncomplete", "未完成委外订单");
        }

        function deletePrePurchasedNotOrdered() {
            deleteData("PrePurchasedNotOrdered", "已预约未下采购单");
        }

        function deleteSalesOrdersForProduction() {
            deleteData("SalesOrdersForProduction", "需生产的销售单");
        }

        function deleteMaterialsWorkshopReceived() {
            deleteData("MaterialsWorkshopReceived", "车间已领");
        }

        function deletePurchaseOrdersIncomplete() {
            deleteData("PurchaseOrdersIncomplete", "未完成采购单");
        }

        function deleteProductionOrdersIncomplete() {
            deleteData("ProductionOrdersIncomplete", "未完成生产单");
        }

        // 打开自定义导入对话框
        function openImportDialog(dataType, title, dataAnalysisId) {
            // 创建对话框内容
            var dialogContent =
                '<div class="import-dialog">' +
                '    <div class="form-group">' +
                '        <div class="file-input-container">' +
                '            <button type="button" class="btn btn-default" id="chooseFileBtn">选择文件</button>' +
                '            <span id="fileNameDisplay">未选择文件</span>' +
                '            <input type="file" id="importFile" name="file" style="display: none;" accept=".xls,.xlsx"/>' +
                '        </div>' +
                '    </div>' +
                '    <div class="form-group" style="margin-top: 15px;">' +
                '        <div class="checkbox">' +
                '            <label>' +
                '                <input type="checkbox" id="updateSupportCheckbox"> 是否更新已经存在的数据' +
                '            </label>' +
                '        </div>' +
                '        <a class="btn btn-default btn-xs" onclick="downloadTemplate(\'' + dataType + '\')">' +
                '            <i class="fa fa-download"></i> 下载模板' +
                '        </a>' +
                '    </div>' +
                '    <div class="form-group" style="margin-top: 10px; color: red;">' +
                '        提示：仅允许导入"xls"或"xlsx"格式文件！' +
                '    </div>' +
                '    <div class="form-group text-center" style="margin-top: 20px;">' +
                '        <button type="button" class="btn btn-primary" onclick="submitImport(\'' + dataType + '\', ' + dataAnalysisId + ')">导入</button>' +
                '        <button type="button" class="btn btn-default" style="margin-left: 10px;" onclick="closeImportDialog()">取消</button>' +
                '    </div>' +
                '</div>';

            // 打开对话框
            layer.open({
                type: 1,
                title: title,
                closeBtn: 1,
                area: ['400px', '300px'],
                content: dialogContent,
                success: function(layero, index) {
                    // 存储对话框索引
                    window.importDialogIndex = index;

                    // 设置文件选择按钮事件
                    $('#chooseFileBtn').on('click', function() {
                        $('#importFile').click();
                    });

                    // 文件选择后显示文件名
                    $('#importFile').on('change', function() {
                        var fileName = $(this).val().split('\\').pop();
                        if (fileName) {
                            $('#fileNameDisplay').text(fileName);
                        } else {
                            $('#fileNameDisplay').text('未选择文件');
                        }
                    });
                }
            });
        }

        // 下载模板
        function downloadTemplate(dataType) {
            var templateUrl = getTemplateUrl(dataType);
            if (templateUrl) {
                // 显示下载中的加载提示
                var loadingIndex = layer.msg('正在获取模板，请稍候......', {
                    icon: 16,
                    shade: 0.3,
                    time: 0 // 不自动关闭
                });

                // 先调用模板API获取文件名
                $.ajax({
                    url: templateUrl,
                    type: "get",
                    dataType: "json",
                    success: function(result) {
                        // 关闭加载提示
                        layer.close(loadingIndex);

                        if (result.code == 0 && result.msg) {
                            // 获取文件名成功，调用下载API
                            var fileName = result.msg;
                            var downloadUrl = ctx + "common/download?fileName=" + encodeURIComponent(fileName) + "&delete=true";
                            window.location.href = downloadUrl;
                        } else {
                            $.modal.alertError("获取模板文件失败：" + result.msg);
                        }
                    },
                    error: function() {
                        // 关闭加载提示
                        layer.close(loadingIndex);
                        $.modal.alertError("获取模板文件失败，请稍后重试");
                    }
                });
            }
        }

        // 关闭导入对话框
        function closeImportDialog() {
            if (window.importDialogIndex !== undefined) {
                layer.close(window.importDialogIndex);
            }
        }

        // 从表格导入对话框下载模板
        function downloadTemplateFromTable() {
            // 获取当前导入的模板URL
            var templateUrl = table.options.importTemplateUrl;
            if (templateUrl) {
                // 显示下载中的加载提示
                var loadingIndex = layer.msg('正在获取模板，请稍候......', {
                    icon: 16,
                    shade: 0.3,
                    time: 0 // 不自动关闭
                });

                // 先调用模板API获取文件名
                $.ajax({
                    url: templateUrl,
                    type: "get",
                    dataType: "json",
                    success: function(result) {
                        // 关闭加载提示
                        layer.close(loadingIndex);

                        if (result.code == 0 && result.msg) {
                            // 获取文件名成功，调用下载API
                            var fileName = result.msg;
                            var downloadUrl = ctx + "common/download?fileName=" + encodeURIComponent(fileName) + "&delete=true";
                            window.location.href = downloadUrl;
                        } else {
                            $.modal.alertError("获取模板文件失败：" + result.msg);
                        }
                    },
                    error: function() {
                        // 关闭加载提示
                        layer.close(loadingIndex);
                        $.modal.alertError("获取模板文件失败，请稍后重试");
                    }
                });
            }
        }

        // 提交导入
        function submitImport(dataType, dataAnalysisId) {
            var fileInput = $('#importFile')[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                $.modal.alertWarning("请选择要导入的文件");
                return;
            }

            var file = fileInput.files[0];
            var fileName = file.name;
            if (!fileName.endsWith('.xls') && !fileName.endsWith('.xlsx')) {
                $.modal.alertWarning("文件格式不正确，请选择 .xls 或 .xlsx 格式的文件");
                return;
            }

            var formData = new FormData();
            formData.append('file', file);
            formData.append('updateSupport', $('#updateSupportCheckbox').prop('checked'));
            formData.append('dataAnalysisId', dataAnalysisId);

            // 显示导入中的加载提示
            var loadingIndex = layer.msg('正在导入中......', {
                icon: 16,
                shade: 0.3,
                time: 0 // 不自动关闭
            });

            $.ajax({
                url: getImportUrl(dataType),
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function(result) {
                    // 关闭加载提示
                    layer.close(loadingIndex);

                    if (result.code == 0) {
                        $.modal.alertSuccess(result.msg);
                        closeImportDialog();

                        // 刷新导入状态
                        var statisticsNumber = $("input[name='statisticsNumber']").val();
                        checkImportStatus(dataType, statisticsNumber);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function() {
                    // 关闭加载提示
                    layer.close(loadingIndex);
                    $.modal.alertError("导入失败，请稍后重试");
                }
            });
        }
    </script>

    <style>
        .import-dialog {
            padding: 20px;
        }
        .file-input-container {
            display: flex;
            align-items: center;
        }
        #fileNameDisplay {
            margin-left: 10px;
            color: #666;
        }
        .layui-layer-title {
            background-color: #f8f8f8;
            border-bottom: 1px solid #eee;
        }
        .btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;
        }
    </style>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
<form enctype="multipart/form-data" class="mt20 mb10">
    <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
            <input type="checkbox" id="updateSupport" name="updateSupport" title="如果数据已经存在，更新这条数据。"> 是否更新已经存在的数据
             &nbsp;	<a onclick="downloadTemplateFromTable()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
            提示：仅允许导入"xls"或"xlsx"格式文件！
        </font>
    </div>
</form>
</script>
</html>